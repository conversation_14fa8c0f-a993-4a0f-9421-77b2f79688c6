"""
This file contains utility functions for the TANN_v4 project.

Date: 2025-03-04
Author: <PERSON><PERSON><PERSON>
"""
#%% ---- Importing Required Libraries ----
import json
import os
import pickle
import random
from pathlib import Path

import matplotlib.pyplot as plt
import numpy as np

#%% ---- Set Global DType ----
GLOBAL_DTYPE = np.float64
#%% ---- Functions ----
# Data-Related Functions
def get_data(folder_name, E, file_names:dict=None):
    '''Read the data from the folder and return the raw data as a numpy array consisting
    strain(e11), stress(s11), state variable(sv1) and free energy.

    Parameters:
    - folder_name (str): The name of the folder containing the data.
    - E (float): Young's modulus.
    - file_names (dict): Dictionary containing the names of the data files.
        Default is None, which uses the default file names.

    Returns:
    - data (numpy.ndarray): Numpy array containing the raw data.
    '''

    if file_names is None:
        file_names = {
            'strain': 'strain.txt',
            'stress': 'stress.txt',
            'state_var': 'state_var.txt'
        }

    base_path = Path(__file__).parent
    data_path = base_path / '../../data' / folder_name

    if not data_path.exists():
        raise FileNotFoundError(f"Data folder '{data_path}' does not exist.")
    if not data_path.is_dir():
        raise NotADirectoryError(f"Path '{data_path}' is not a directory.")

    files = {
        name: data_path / file_name
        for name, file_name in file_names.items()
    }

    for name, file_path in files.items():
        if not file_path.exists():
            raise FileNotFoundError(f"File '{file_path}' does not exist.")
        if not file_path.is_file():
            raise IsADirectoryError(f"Path '{file_path}' is not a file.")

    try:
        strain = np.loadtxt(files['strain'], dtype=GLOBAL_DTYPE)
        stress = np.loadtxt(files['stress'], dtype=GLOBAL_DTYPE)
        state = np.loadtxt(files['state_var'], dtype=GLOBAL_DTYPE)
    except ValueError as e:
        raise ValueError(f"Error reading data files: {e}")

    n = len(strain)
    data=np.empty((n, 4), dtype=GLOBAL_DTYPE)

    data[:, 0] = strain[:, 0]           # strain
    data[:, 1] = stress[:, 0]           # stress
    data[:, 2] = state[:, 0]            # state variable
    data[:, 3] = stress[:, 0]**2/2/E    # free energy

    return data

def pre_process_data(data):
    '''Pre-process the raw data to get the inputs and targets of both networks NNf and NNz.
    + For NNf:
        - inputs: columns 0, 1, and 2
        - target: column 3
        - secondary output: column 4
    + For NNz:
        - inputs: columns 0, 1, 5, and 6
        - target: column 2

    Return:
    - TANN-compatible preprocessed data array.
    '''

    n = len(data)
    pre_processed = np.empty((n-1, 9), dtype=GLOBAL_DTYPE)

    # Initialize ζ_t with the initial value comes from state variables received from driver.
    ζ_t = data[0, 2]
    assert ζ_t == 0, f"Initial plastic strain is not zero. It is {ζ_t}. Check the data."

    for i in range(1, n):
        Dε = data[i, 0] - data[i-1, 0]                          # total strain increment
        D_eq_pl_ε = data[i, 2] - data[i-1, 2]                   # equivalent plastic strain increment
        Dζ = np.sign(Dε) * D_eq_pl_ε                            # Dζ: NOTE: plastic strain increment
        acc_eq_pl_ε_t = data[i-1, 2]                            # accumulated equivalent plastic strain @ time=t

        # NNf necessary columns
        pre_processed[i-1, 0] = Dε                              # Dε                (total strain increment)
        pre_processed[i-1, 1] = data[i-1, 1]                    # σ_t               (stress @ time=t)
        pre_processed[i-1, 2] = Dζ                              # Dζ                (plastic strain increment)
        pre_processed[i-1, 3] = data[i, 3]                      # F_tdt             (free energy @ time=t+dt)
        pre_processed[i-1, 4] = data[i, 1]                      # σ_tdt             (stress @ time=t+dt)
        # Additional columns for NNz
        pre_processed[i-1, 5] = data[i, 0]                      # ε_tdt             (total strain @ time=t+dt)
        pre_processed[i-1, 6] = ζ_t                             # ζ_t               (platic strain @ time=t). NOTE: This is not the driver state variable.

        pre_processed[i-1, 7] = acc_eq_pl_ε_t                   # accumulated equivalent plastic strain @ time=t
        pre_processed[i-1, 8] = D_eq_pl_ε                       # equivalent plastic strain increment

        ζ_t += Dζ    # Incrementally make the plastic strains.

    return pre_processed

def split_dataset (folder_name, E, train_size:int, val_size:int, test_size:int, random_state=42, plot_distributions=True):
    """
    Split a sngle raw dataset into training, validation, and test sets with similar distributions.

    Parameters:
    - folder_name (str): The name of the folder containing the raw data.
    - E (float): Young's modulus.
    - train_size (int): Number of samples for training set.
    - val_size (int): Number of samples for validation set.
    - test_size (int): Number of samples for test set.
    - random_state (int): Random seed for reproducibility.
    - plot_distributions (bool): Whether to plot the distributions of the sets.

    Returns:
    - train_data (numpy.ndarray): Training data.
    - val_data (numpy.ndarray): Validation data.
    - test_data (numpy.ndarray): Test data.
    """
    from sklearn.model_selection import train_test_split

    processed_data = pre_process_data(get_data(folder_name, E))

    # Check if we have enough samples
    total_samples = processed_data.shape[0]
    required_samples = train_size + val_size + test_size
    if total_samples < required_samples:
        raise ValueError(f"Not enough samples in the dataset. Required: {required_samples}, Available: {total_samples}")

    # if zero_mask = processed_data[:, 8] == 0, it's also the same as processed_data[:, 2] == 0,
    # since index 8 is the equivalent plastic strain increment, and index 2 is the plastic strain increment.
    # Therefore, we can use either one of them to split the data into zero and non-zero samples, based on the network we are training.
    zero_mask = processed_data[:, 2] == 0
    zero_samples = processed_data[zero_mask]
    nonzero_samples = processed_data[~zero_mask]

    total_size = train_size + val_size + test_size
    train_prop = train_size / total_size
    val_prop = val_size / total_size

    # Split zero samples into train, val, and test sets
    zero_train, zero_temp = train_test_split(
        zero_samples,
        train_size=train_prop,
        random_state=random_state,
        shuffle=True
    )
    zero_val, zero_test = train_test_split(
        zero_temp,
        train_size=val_prop/(1 - train_prop),
        random_state=random_state,
        shuffle=True
    )
    # Split non-zero samples into train, val, and test sets
    nonzero_train, nonzero_temp = train_test_split(
        nonzero_samples,
        train_size=train_prop,
        random_state=random_state,
        shuffle=True
    )
    nonzero_val, nonzero_test = train_test_split(
        nonzero_temp,
        train_size=val_prop/(1 - train_prop),
        random_state=random_state,
        shuffle=True
    )

    # Combine zero and non-zero samples
    train_data = np.vstack((zero_train, nonzero_train))
    val_data = np.vstack((zero_val, nonzero_val))
    test_data = np.vstack((zero_test, nonzero_test))

    np.random.seed(random_state)
    np.random.shuffle(train_data)
    np.random.shuffle(val_data)
    np.random.shuffle(test_data)

    if plot_distributions:

        feature_names = ['Dε', 'σ_t', 'Dζ', 'F_tdt', 'σ_tdt', 'ε_t', 'ζ_t']

        # Create a figure with subplots for each feature
        fig, axes = plt.subplots(2, 4, figsize=(16, 8))
        axes = axes.flatten()

        # Plot histograms for each feature
        for i, name in enumerate(feature_names):
            if i < len(axes) and i < processed_data.shape[1]:
                ax = axes[i]

                # Get feature values for each dataset
                train_feature = train_data[:, i]
                val_feature = val_data[:, i]
                test_feature = test_data[:, i]

                # Determine common range for all histograms
                min_val = min(train_feature.min(), val_feature.min(), test_feature.min())
                max_val = max(train_feature.max(), val_feature.max(), test_feature.max())

                # Plot histograms
                ax.hist(train_feature, bins=30, alpha=0.8, label='Training',
                        range=(min_val, max_val), color='blue', histtype='step')
                ax.hist(val_feature, bins=30, alpha=0.5, label='Validation',
                        range=(min_val, max_val), color='crimson', histtype='stepfilled')
                ax.hist(test_feature, bins=30, alpha=0.65, label='Test',
                        range=(min_val, max_val), edgecolor='black',  color='darkslategrey', histtype='stepfilled')
                ax.set_xlabel(name)
                ax.set_ylabel('Frequency')

                # Use log scale for y-axis to better visualize distribution
                ax.set_yscale('log')

                # Only add legend to the first subplot
                if i == 0:
                    ax.legend()

        plt.tight_layout()
        plt.show()

    return train_data, val_data, test_data

def process_data(train_data, val_data, test_data=None, eps=1e-8):
    '''Normalize the datasets and extract the normalizing parameters.
    Parameters:
    - train_data (array-like): Training data.
    - val_data (array-like): Validation data.
    - test_data (array-like, optional): Test data.
    '''
    assert train_data.shape[1] == val_data.shape[1]
    num_train_samples = train_data.shape[0]
    TrVal_data = np.vstack((train_data, val_data))
    n_TrVal_data, norm_params = normalize_data(TrVal_data, eps=eps)
    n_train_data = n_TrVal_data[: num_train_samples, :]
    n_val_data = n_TrVal_data[num_train_samples:, :]
    if test_data is not None:
        assert train_data.shape[1] == test_data.shape[1]
        n_test_data = apply_normalization(data=test_data, normal_params=norm_params)
        return n_train_data, n_val_data, n_test_data, norm_params
    else:
        return n_train_data, n_val_data, norm_params

def normalize_data(data, eps=1e-8):
    '''Normalize the data using the Min-Max normalization and extract the normalizing parameters.'''
    norm_params = get_α_β(data, eps=eps)
    nrml_data = apply_normalization(data, norm_params)
    return nrml_data, norm_params

def get_α_β(data, norm=True, norm_01=False, no_norm=False, eps=1e-8):
    ''' Compute the normalization parameters (α & β).\n
        "no_norm" is for the case when normalizing/standardizing is not considered.\n
        "norm_01" is for the case when the scaling range is [0, 1].'''
    n_rows, n_cols = data.shape
    norm_params = list()

    for i in range(0, n_cols):
        u = data[:, i]
        if no_norm == False:
            if norm == True:
                if norm_01 == False:
                    u_max = np.max(u)
                    u_min = np.min(u)
                    ## DEV: For pure elastic datasets where Dz_min = Dz_max = 0
                    if np.abs(u_max - u_min) < eps:
                        α=1; β=u_min
                    else:
                        α = (u_max - u_min) / 2.; β = np.average((u_max, u_min))
                else: α = np.max(u); β = 0.
            elif norm == False:
                α = np.std(u, axis=0); β = np.mean(u, axis=0)
        else: α = 1.; β = 0.

        norm_params.append((GLOBAL_DTYPE(α), GLOBAL_DTYPE(β)))

    return np.array(norm_params, dtype=GLOBAL_DTYPE)

def apply_normalization(data, normal_params: list):
    '''Normalize the data using the given normalizing parameters.'''
    n_rows, n_cols = data.shape
    nrml_data = np.empty((n_rows, n_cols), dtype=GLOBAL_DTYPE)
    assert n_cols == len(normal_params)
    for i in range(0, n_cols):
        u = data[:, i]
        α = normal_params[i][0]; β = normal_params[i][1]
        nrml_data[:, i] = (u - β) / α

    return nrml_data

def oversample_minority_class(folder_name, E, train_size=1500, val_size=750, sampling_strategy=0.5, random_state=42):
    """
    Load data, preprocess it, and apply SMOTE oversampling to the minority class (non-zero targets).

    Parameters:
    ----------
    folder_name : str
        Name of the data folder (e.g., '10.00')
    E : float
        Young's modulus
    train_size : int, default=1500
        Number of samples for training set before oversampling
    val_size : int, default=750
        Number of samples for validation set before oversampling
    sampling_strategy : float, default=0.5
        Desired ratio of non-zero samples to zero samples after resampling.
        For example, 0.5 means the non-zero samples will be 50% of the zero samples.
    random_state : int, default=42
        Random seed for reproducibility

    Returns:
    -------
    train_data_resampled : array-like
        Oversampled training data
    val_data_resampled : array-like
        Oversampled validation data
    """
    from imblearn.over_sampling import SMOTE

    train_data, val_data, test_data = split_dataset(folder_name, E,
                                          train_size=train_size,
                                          val_size=val_size,
                                          test_size=750,
                                          random_state=random_state,
                                          plot_distributions=False)

    # Function to apply SMOTE to a dataset
    def apply_smote(data, sampling_strategy):
        # Extract features for SMOTE
        X = data[:, [0, 1, 5, 6]]  # [Dε, σ_t, ε_t, ζ_t]
        y = data[:, 2]  # Dζ (target)

        # Create binary labels for SMOTE (0 for zero values, 1 for non-zero)
        binary_labels = (y != 0).astype(int)

        # Initialize SMOTE
        smote = SMOTE(sampling_strategy=sampling_strategy,
                     random_state=random_state,
                     k_neighbors=min(5, sum(binary_labels) - 1))  # Adjust neighbors based on minority class size

        # Fit and apply SMOTE
        X_resampled, y_binary_resampled = smote.fit_resample(X, binary_labels)

        y_resampled = np.zeros(len(y_binary_resampled), dtype=GLOBAL_DTYPE)

        # For non-zero Dζ, maintain sign consistency with Dε
        nonzero_mask = y_binary_resampled == 1
        nonzero_indices = np.where(nonzero_mask)[0]

        if len(nonzero_indices) > 0:
            # Get original non-zero values and their corresponding strain increments
            orig_nonzero_mask = y != 0
            orig_Dζ = y[orig_nonzero_mask]
            orig_Dε = data[orig_nonzero_mask][:, 0]  # Corresponding strain increments

            orig_ratios = orig_Dζ / orig_Dε

            # For synthetic samples, ensure sign consistency
            for idx in nonzero_indices:
                Dε = X_resampled[idx, 0]  # Get strain increment
                abs_Dε = np.abs(Dε)
                # Generate synthetic Dζ with correct sign
                if Dε > 0:
                    pos_ratios = orig_ratios[orig_Dε > 0]
                    if len(pos_ratios) > 0:
                        ratio = np.clip(np.random.normal(
                            np.mean(pos_ratios),
                            np.std(pos_ratios)
                        ), 0, 0.99)  # Ensure ratio < 1
                        y_resampled[idx] = ratio * abs_Dε
                elif Dε < 0:
                    neg_ratios = orig_ratios[orig_Dε < 0]
                    if len(neg_ratios) > 0:
                        # Generate ratio between 0 and 1, biased towards original ratios
                        ratio = np.clip(np.random.normal(
                            np.mean(neg_ratios),
                            np.std(neg_ratios)
                        ), 0, 0.99)  # Ensure ratio < 1
                        y_resampled[idx] = -ratio * abs_Dε

        # Reconstruct the full dataset with oversampled points
        data_resampled = np.zeros((len(X_resampled), data.shape[1]), dtype=GLOBAL_DTYPE)
        data_resampled[:, [0, 1, 5, 6]] = X_resampled  # Features
        data_resampled[:, 2] = y_resampled  # Target (Dζ)

        # Calculate F_tdt and σ_tdt for synthetic samples
        E_local = E  # Using the global Young's modulus
        data_resampled[:, 4] = data_resampled[:, 1] + E_local * (data_resampled[:, 0] - data_resampled[:, 2])  # σ_tdt
        data_resampled[:, 3] = 0.5 * data_resampled[:, 4] * data_resampled[:, 4] / E_local  # F_tdt

        return data_resampled

    # Apply SMOTE to training set
    train_data_resampled = apply_smote(train_data, sampling_strategy)

    # Print statistics about the resampling
    def print_class_stats(data, name):
        zero_count = np.sum(data[:, 2] == 0)
        nonzero_count = len(data) - zero_count
        print(f"{name} set:")
        print(f"  Zero samples: {zero_count} ({100 * zero_count / len(data):.1f}%)")
        print(f"  Non-zero samples: {nonzero_count} ({100 * nonzero_count / len(data):.1f}%)")

    print("\nBefore oversampling:")
    print_class_stats(train_data, "Training")
    print_class_stats(val_data, "Validation")
    print("\nAfter oversampling:")
    print_class_stats(train_data_resampled, "Training")

    return train_data_resampled, val_data, test_data

def get_ROS_data(folder_name, E, train_size=1500, val_size=750, ratio=0.5, random_state=42):
    """
    Random Oversampling of the minority class (non-zero targets) by simple copying.

    Parameters:
    ----------
    ratio : float, default=0.5
        Desired ratio of (non-zero samples)/(zero samples) after oversampling.
        For example:
        - ratio=0.5 means non-zero samples will be half of zero samples
        - ratio=1.0 means equal number of zero and non-zero samples
        - ratio=0.2 means non-zero samples will be 20% of zero samples
    """
    train_data, val_data, test_data = split_dataset(folder_name, E,
                                                   train_size=train_size,
                                                   val_size=val_size,
                                                   test_size=750,
                                                   random_state=random_state,
                                                   plot_distributions=False)

    resampled_datasets = []
    for data in [train_data, val_data]:
        nonzero_mask = data[:, 2] != 0
        nonzero_samples = data[nonzero_mask]
        zero_samples = data[~nonzero_mask]

        # Calculate required number of copies
        n_zero = len(zero_samples)
        n_nonzero_desired = int(n_zero * ratio)  # Direct ratio of zero samples
        n_copies = n_nonzero_desired - len(nonzero_samples)

        if n_copies > 0:
            # Randomly sample with replacement
            np.random.seed(random_state)
            copies_indices = np.random.choice(len(nonzero_samples), n_copies)
            copies = nonzero_samples[copies_indices]

            # Combine original and copied samples
            data_resampled = np.vstack([data, copies])
            np.random.shuffle(data_resampled)  # Shuffle to mix copies with originals
        else:
            data_resampled = data
        resampled_datasets.append(data_resampled)

    train_data_resampled, val_data_resampled = resampled_datasets

    # Print statistics about the resampling
    def print_class_stats(data, name):
        zero_count = np.sum(data[:, 2] == 0)
        nonzero_count = len(data) - zero_count
        print(f"{name} set:")
        print(f"  Zero samples: {zero_count}")
        print(f"  Non-zero samples: {nonzero_count}")
        print(f"  Ratio (non-zero/zero): {nonzero_count/zero_count:.3f}")

    print("\nBefore oversampling:")
    print_class_stats(train_data, "Training")
    print("\nAfter oversampling:")
    print_class_stats(train_data_resampled, "Training")

    return train_data_resampled, val_data_resampled, test_data

# Error-Related Functions
def compute_error(y_pred, y_true):
    '''Compute the error between the predicted and true values.

    Parameters:
    - y_pred (array-like): Predicted values.
    - y_true (array-like): True values.

    Returns:
    - errors_dict (dict): Dictionary containing error arrays and their averages.
    '''
    y_pred = np.array(y_pred, dtype=GLOBAL_DTYPE).reshape(-1, 1)
    y_true = np.array(y_true, dtype=GLOBAL_DTYPE).reshape(-1, 1)
    assert y_pred.shape == y_true.shape

    # error arrays
    epsilon = 1e-10
    err_mape = np.abs((y_true - y_pred) / (y_true + epsilon)) * 100
    err_mape[err_mape > 100] = 100
    err_mae = np.abs(y_true - y_pred)
    err_mse = np.square(y_true - y_pred)
    err_rmse = np.sqrt(err_mse)

    # average error
    avg_mape = np.mean(err_mape)
    avg_mae = np.mean(err_mae)
    avg_mse = np.mean(err_mse)
    avg_rmse = np.mean(err_rmse)

    errors_dict = {
        'mape': {'errors': err_mape, 'average': avg_mape},
        'mae': {'errors': err_mae, 'average': avg_mae},
        'mse': {'errors': err_mse, 'average': avg_mse},
        'rmse': {'errors': err_rmse, 'average': avg_rmse}
    }

    return errors_dict

def compute_r2_score(y_pred, y_true):
    '''Compute the R2 score.'''
    y_pred = np.array(y_pred, dtype=GLOBAL_DTYPE).reshape(-1, 1)
    y_true = np.array(y_true,dtype=GLOBAL_DTYPE).reshape(-1, 1)
    ss_res = np.sum((y_true - y_pred) ** 2)
    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
    r2_score = 1 - (ss_res / ss_tot)
    return r2_score

# Input File Creators
def create_randomload_inpfile (material, no_inc, strain_range = 0.02, model_address='', no_state_vars=3, load_type=4,
                               folder='../../material_dvlp/driver/inp', file_name='drive_v3.inp'):
    """
    Create an input file for the driver code with random loading increments.

    Parameters:
    - material (numpy array): array of material properties
    material = [E, v, σ_y, K, eps_0, m]
    - no_inc (int): number of increments
    - strain_range (float): the range in which the random strain increments varies.
    For example, if strain_range=0.02, the random strain increments will be between -0.02 and 0.02.
    - model_adress (str): the address of the model file
    - no_state_vars (int): number of state variables
    - load_type (int): type of loading. NOTE! Refer to the driver code help for more information.
    - folder (str): the folder in which the input file will be saved
    file_name (str): the name of the input file
    """

    matstr = np.array2string(material)[1:-1]

    loading = ''
    for inc in range(1, no_inc+1):
        random_e = str(f"{random.uniform(-strain_range, strain_range):.7f}")
        if inc < no_inc:
            loading += ('1 4 5 6' + '\n' +
                        '1 ' + random_e + ' 0.0 0.0 0.0 0.0 0.0 1.0' + '\n')
        else:
            loading += ('1 4 5 6' + '\n' +
                        '1 ' + random_e + ' 0.0 0.0 0.0 0.0 0.0 1.0')
            # the first number is 1
            # >> it means that it creats a series of delta_epsilons.


    txt = ('INCLUDE ' + model_address + '\n' +
           'STATE_VAR ' + str(no_state_vars) + '\n' +
           'LOAD_TYPE ' + str(load_type) + '\n' +
           'BEGIN_MATERIAL' + '\n' +
           matstr + '\n' +
           'END_MATERIAL' + '\n' +
           'BEGIN_LOAD' + '\n' +
           loading + '\n' +
           'END_LOAD')

    with open(folder + '/' + file_name, 'w') as file:
        file.write(txt)
        file.close()

def create_monoload_inpfile (material, no_inc, model_address='', no_state_vars=3, load_type=4,
                             folder='../../material_dvlp/driver/inp', file_name='drive_v3.inp'):
    """
    Create an input file for the driver code with monotonic loading increments.

    Parameters:
    - material (numpy array): array of material properties
    material = [E, v, σ_y, K, eps_0, m]
    - no_inc (lis): List of number of increments
    no_inc = [no_el_inc, no_pl_inc, no_unload_inc]
    - model_adress (str): the address of the model file
    - no_state_vars (int): number of state variables
    - load_type (int): type of loading. NOTE! Refer to the driver code help for more information.
    - folder (str): the folder in which the input file will be saved
    """
    no_el_inc = no_inc[0]
    no_pl_inc = no_inc[1]
    no_unload_inc = no_inc[2]
    eps_y = material[2]/ material[0]                # the yield strain
    matstr = np.array2string(material)[1:-1]
    loading=''

    eps_inc = str(f"{(eps_y / (1*no_el_inc)):.7f}")         # the fixed elastic strain increment
    eps_unload_inc = str(f"{(-eps_y / (1*no_el_inc)):.6f}") # the fixed unloading strain increment

    if no_unload_inc == False:
        for inc in range(1, no_el_inc + no_pl_inc + 1):
            if inc < no_el_inc + no_pl_inc:
                loading += ('1 4 5 6' + '\n' +
                            '1 ' + eps_inc + ' 0.0 0.0 0.0 0.0 0.0 1.0' + '\n')
            else:
                loading += ('1 4 5 6' + '\n' +
                            '1 ' + eps_inc + ' 0.0 0.0 0.0 0.0 0.0 1.0')
    else:
        for inc in range(1, no_el_inc + no_pl_inc + 1):
            loading += ('1 4 5 6' + '\n' +
                        '1 ' + eps_inc + ' 0.0 0.0 0.0 0.0 0.0 1.0' + '\n')
        for inc in range(1, no_unload_inc + 1):
            if inc < no_unload_inc:
                loading += ('1 4 5 6' + '\n' +
                '1 ' + eps_unload_inc + ' 0.0 0.0 0.0 0.0 0.0 1.0' + '\n')
            else:
                loading += ('1 4 5 6' + '\n' +
                '1 ' + eps_unload_inc + ' 0.0 0.0 0.0 0.0 0.0 1.0')

    txt = ('INCLUDE ' + model_address + '\n' +
        'STATE_VAR ' + str(no_state_vars) + '\n' +
        'LOAD_TYPE ' + str(load_type) + '\n' +
        'BEGIN_MATERIAL' + '\n' +
        matstr + '\n' +
        'END_MATERIAL' + '\n' +
        'BEGIN_LOAD' + '\n' +
        loading + '\n' +
        'END_LOAD')

    with open(folder + '/' + file_name, 'w') as file:
        file.write(txt)
        file.close()

#%% Training History Functions
def save_train_history(history, save_path, filename='training_history'):
    """
    Save the training history object to a file for later analysis.

    Parameters:
    - history: Keras History object returned from model.fit()
    - save_path: Directory path where to save the history
    - filename: Name of the file (without extension)

    Returns:
    - full_path: Path to the saved history file
    """
    os.makedirs(save_path, exist_ok=True)

    # Convert history.history to a regular dictionary if it's a keras.callbacks.History object
    if hasattr(history, 'history'):
        history_dict = history.history
    else:
        history_dict = history

    # Save as pickle for complete object preservation
    pickle_path = os.path.join(save_path, f"{filename}.pkl")
    with open(pickle_path, 'wb') as f:
        pickle.dump(history_dict, f)

    # Also save as JSON for human readability
    json_path = os.path.join(save_path, f"{filename}.json")
    with open(json_path, 'w') as f:
        json.dump(history_dict, f, indent=4)

def load_train_history(file_path):
    """
    Load a previously saved training history.

    Parameters:
    - file_path: Path to the saved history file (.pkl)

    Returns:
    - history_dict: Dictionary containing the training history
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"History file not found at {file_path}")

    # Determine file type from extension
    if file_path.endswith('.pkl'):
        with open(file_path, 'rb') as f:
            history_dict = pickle.load(f)
    elif file_path.endswith('.json'):
        with open(file_path, 'r') as f:
            history_dict = json.load(f)
    else:
        raise ValueError("Unsupported file format. Use .pkl or .json files.")

    return history_dict