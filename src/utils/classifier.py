"""
Binary classifier for predicting whether Δζ should be zero or non-zero.

This module provides a simple binary classifier that can be used alongside
the NNz regression model to improve predictions for zero-valued targets.

Date: 2025-05-14
Author: <PERSON><PERSON><PERSON>ahnejad
"""
import json
import os
import pickle
from pathlib import Path

import numpy as np
import tensorflow as tf
from tensorflow.keras.callbacks import EarlyStopping
from tensorflow.keras.layers import Dense, Dropout
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.optimizers import Adam


class DzBinaryClassifier:
    def __init__(self, hidden_dims=[32, 16], dropout_rate=0.2):
        """
        Initialize the binary classifier for predicting whether Δζ should be zero or non-zero.

        Parameters:
        - hidden_dims (list): List of integers representing the number of nodes in each hidden layer.
        - dropout_rate (float): Dropout rate for regularization.
        """
        self.hidden_dims = hidden_dims
        self.dropout_rate = dropout_rate
        self.model = None
        self.threshold = 0.5  # Default threshold
        self.scaler = None  # Will store feature scaling parameters

    def build_model(self, input_shape=(3, )):
        """
        Build the binary classification model.

        Parameters:
        - input_shape (tuple): Shape of the input features.
        """
        model = Sequential()

        # First hidden layer
        model.add(Dense(self.hidden_dims[0], activation='relu', input_shape=input_shape))
        model.add(Dropout(self.dropout_rate))

        # Additional hidden layers
        for units in self.hidden_dims[1:]:
            model.add(Dense(units, activation='relu'))
            model.add(Dropout(self.dropout_rate))

        # Output layer (binary classification)
        model.add(Dense(1, activation='sigmoid'))

        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='binary_crossentropy',
            metrics=['accuracy']
        )

        self.model = model
        return model

    def train(self, X_train, y_train, X_val=None, y_val=None, epochs=100, batch_size=32, patience=20):
        """
        Train the binary classifier.

        Parameters:
        - X_train (array): Training features.
        - y_train (array): Training labels (0 for zero Δζ, 1 for non-zero Δζ).
        - X_val (array, optional): Validation features.
        - y_val (array, optional): Validation labels.
        - epochs (int): Maximum number of training epochs.
        - batch_size (int): Batch size for training.
        - patience (int): Patience for early stopping.

        Returns:
        - history: Training history.
        """
        if self.model is None:
            self.build_model((X_train.shape[1],))

        validation_data = None
        if X_val is not None and y_val is not None:
            validation_data = (X_val, y_val)

        early_stopping = EarlyStopping(
            monitor='val_loss' if validation_data else 'loss',
            patience=patience,
            restore_best_weights=True
        )

        history = self.model.fit(X_train, y_train,
                                 epochs=epochs,
                                 batch_size=batch_size,
                                 validation_data=validation_data,
                                 callbacks=[early_stopping],
                                 verbose=1
        )

        return history

    def optimize_threshold(self, X_val, y_val, metric='f1'):
        """
        Find the optimal threshold for classification based on validation data.

        Parameters:
        - X_val (array): Validation features.
        - y_val (array): Validation labels.
        - metric (str): Metric to optimize ('f1', 'accuracy', 'precision', or 'recall').

        Returns:
        - optimal_threshold (float): The optimal threshold.
        """
        from sklearn.metrics import (accuracy_score, f1_score, precision_score, recall_score)

        if self.model is None:
            raise ValueError("Model has not been trained yet.")

        # Get probabilities
        probabilities = self.model.predict(X_val, verbose=0)            # NOTE: the 'predict' here is a method of the "Sequential" model class in Keras.

        # Try different thresholds
        thresholds = np.linspace(0.1, 0.9, 9)
        best_score = 0
        best_threshold = 0.5

        for threshold in thresholds:
            predictions = (probabilities >= threshold).astype(int)

            if metric == 'f1':
                score = f1_score(y_val, predictions)
            elif metric == 'accuracy':
                score = accuracy_score(y_val, predictions)
            elif metric == 'precision':
                score = precision_score(y_val, predictions)
            elif metric == 'recall':
                score = recall_score(y_val, predictions)
            else:
                raise ValueError(f"Unknown metric: {metric}")

            if score > best_score:
                best_score = score
                best_threshold = threshold

        self.threshold = best_threshold
        print(f"Optimal threshold: {best_threshold} with {metric} score: {best_score:.4f}")
        return best_threshold

    def predict(self, X, threshold=None):
        """
        Predict whether Δζ should be zero or non-zero.

        Parameters:
        - X (array): Input features.
        - threshold (float, optional): Classification threshold. If None, use the instance threshold.

        Returns:
        - predictions (array): Binary predictions (0 or 1).
        - probabilities (array): Probability of non-zero Δζ.
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet.")

        if threshold is None:
            threshold = self.threshold

        probabilities = self.model.predict(X, verbose=0)                # NOTE: the 'predict' here is a method of the "Sequential" model class in Keras.
        predictions = (probabilities >= threshold).astype(int)

        return predictions, probabilities

    def save(self, save_dir):
        """
        Save the trained model and its configuration.

        Parameters:
        - save_dir (str): Directory to save the model.

        Returns:
        - save_path (str): Path where the model was saved.
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet.")

        os.makedirs(save_dir, exist_ok=True)
        save_dir = Path(save_dir)

        model_path = os.path.join(save_dir, 'binary_classifier_model.h5')
        self.model.save(model_path)

        # Save configuration
        metadata = {
            'hidden_dims': self.hidden_dims,
            'dropout_rate': self.dropout_rate,
            'threshold': self.threshold
        }

        metadata_path = save_dir / 'metadata.json'
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=4)

        print(f"Model saved to {save_dir}")
        return save_dir

    @classmethod
    def load(cls, load_dir):
        """
        Load a trained model and its configuration.

        Parameters:
        - load_dir (str): Directory containing the saved model.

        Returns:
        - classifier (DzBinaryClassifier): Loaded classifier instance.
        """
        # Load configuration
        load_dir = Path(load_dir)
        metadata_path = load_dir / 'metadata.json'
        with open(metadata_path, 'r') as f:
            metadata = json.load(f)

        # Create instance with loaded configuration
        classifier_instance = cls(
            hidden_dims=metadata['hidden_dims'],
            dropout_rate=metadata['dropout_rate']
        )

        model_path = os.path.join(load_dir, 'binary_classifier_model.h5')
        classifier_instance.model = load_model(model_path)

        classifier_instance.threshold = metadata['threshold']

        return classifier_instance

# Helper function to prepare data for the binary classifier
def process_data_for_classifier(data):
    """
    Prepare data for the binary classifier by extracting features and creating binary labels.

    Parameters:
    - data (array): Preprocessed data array.

    Returns:
    - X (array): Features for the binary classifier.
    - y (array): Binary labels (0 for zero Δζ, 1 for non-zero Δζ).
    """
    # Extract features: [Dε, σ_t, ζ_t]
    X = data[:, [0, 1, 7]]

    # Create binary labels (0 for zero Δζ, 1 for non-zero Δζ)
    y = (data[:, 8] != 0).astype(int)

    return X, y

def apply_classifier_to_predictions(pred_Dζ, test_data, binary_classifier, method='hard', threshold=None):
    """
    Apply binary classifier to improve NNz predictions, especially for zero-valued targets.

    Parameters:
    - pred_Dζ (array): Original predictions from NNz model.
    - test_data (array): Test data used for predictions.
    - binary_classifier (DzBinaryClassifier): Trained binary classifier.
    - method (str): Method to combine predictions:
        - 'hard': Binary decision (set to 0 if classifier predicts 0)
        - 'soft': Weight predictions by classifier confidence
        - 'adaptive': Use threshold based on prediction magnitude
        - 'hybrid': Combine confidence and magnitude
    - threshold (float, optional): Custom threshold for binary classification.

    Returns:
    - improved_predictions (array): Improved predictions after applying binary classifier.
    """
    # Prepare data for binary classifier
    X_test, _ = process_data_for_classifier(test_data)

    # Get binary predictions and probabilities
    binary_predictions, binary_probabilities = binary_classifier.predict(X_test, threshold=threshold)

    # Create a copy of the original predictions
    improved_predictions = pred_Dζ.copy()

    if method == 'hard':
        # Hard threshold (binary decision)
        for i in range(len(binary_predictions)):
            if binary_predictions[i] == 0:  # If binary classifier predicts zero
                improved_predictions[i] = 0.0

    elif method == 'soft':
        # Soft threshold (confidence-weighted)
        for i in range(len(binary_probabilities)):
            # binary_probabilities[i] is the probability of being non-zero
            # Scale the prediction by this probability
            improved_predictions[i] = pred_Dζ[i] * binary_probabilities[i][0]

    elif method == 'adaptive':
        # Adaptive threshold (based on prediction magnitude)
        for i in range(len(binary_probabilities)):
            # If the binary classifier is confident it's zero (prob < 0.3) or
            # the prediction is very small, set to zero
            if binary_probabilities[i][0] < 0.3 or abs(pred_Dζ[i]) < 1e-6:
                improved_predictions[i] = 0.0

    elif method == 'hybrid':
        # Hybrid approach (combine confidence and magnitude)
        for i in range(len(binary_probabilities)):
            # Calculate a confidence factor based on both classifier and prediction magnitude
            zero_confidence = 1 - binary_probabilities[i][0]  # Confidence it's zero
            magnitude_factor = np.tanh(abs(pred_Dζ[i]) * 1e5)  # Scale based on magnitude

            # If combined confidence is high, set to zero
            if zero_confidence > 0.7 or (zero_confidence > 0.5 and magnitude_factor < 0.3):
                improved_predictions[i] = 0.0
    else:
        raise ValueError(f"Unknown method: {method}. Use 'hard', 'soft', 'adaptive', or 'hybrid'.")

    return improved_predictions
