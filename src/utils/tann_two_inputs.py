"""
This file contains a modified TANN model class that uses only two inputs (Dε and σ_t).
This variant is used for debugging purposes for an elastic material.

Date: 2025-04-11
Author: <PERSON><PERSON><PERSON>
"""
#%% ---- Import Required Libraries ----
import inspect
import json
import logging
import os
from datetime import datetime

import matplotlib.pyplot as plt
import numpy as np

import tensorflow as tf
from sub_nn import GLOBAL_DTYPE, NNf, activation_dict, custom_act
from tann_debug import TannDebugClass

#%% ---- Model Class ----
class NNf_TwoInputs(NNf):
    """A variant of the NNf model that only uses two inputs (Dε and σ_t)."""

    def __init__(self, norm_params, hidden_dims, activation_func='custom_act'):
        """
        Configure the layers of the model based on the given number of hidden layers (excl. output layer) and nodes.

        Parameters:
        - norm_params: Normalization parameters for inputs and outputs
        - hidden_dims (list): List of integers representing the number of nodes in each hidden layer.
        - activation_func (str): Activation function for the hidden layers.
        """
        # Call the parent class constructor but override num_inputs
        super(NNf, self).__init__()  # Call the grandparent constructor (tf.keras.Model)
        self.num_inputs = 2  # Override to use only 2 inputs
        self.num_outputs = 1
        self.activation_func = activation_func
        self.norm_params = norm_params

        self.setup_logger()

        # Create hidden layers
        self.hidden_layers = []
        for i, h_dim in enumerate(hidden_dims):
            self.hidden_layers.append(tf.keras.layers.Dense(
                h_dim,
                activation=self.activation_func,
                name=f'NNf_TwoInputs_Hlayer{i+1}',
            ))
        self.out_layer = tf.keras.layers.Dense(self.num_outputs, name='NNf_TwoInputs_OutLayer')

        # Build the model with 2 inputs
        self.build((None, self.num_inputs))

    def call(self, inputs):
        """
        Forward pass through the network.

        Parameters:
        - inputs: A tensor of shape (batch_size, 2), containing [n_Dε, n_σ_t]

        Returns:
        - n_F_tdt: The normalized predicted free energy
        """
        # Extract the two inputs
        inp_Dε = tf.slice(inputs, [0, 0], [-1, 1])
        inp_σ_t = tf.slice(inputs, [0, 1], [-1, 1])

        # Concatenate inputs
        n_concat = tf.concat([inp_Dε, inp_σ_t], axis=1, name='inputs_concat_of_NNf_TwoInputs')

        # Pass through hidden layers
        for layer in self.hidden_layers:
            n_concat = layer(n_concat)
        n_F_tdt = self.out_layer(n_concat)

        return n_F_tdt

    def get_analytic_F(self, input_i):
        """
        Compute the analytical free energy F_tdt.

        Parameters:
        - input_i: A tensor of shape (batch_size, 2), containing [n_Dε, n_σ_t]

        Returns:
        - n_F_tdt_analytic: The normalized analytical free energy
        """
        n_Dε = tf.slice(input_i, [0, 0], [-1, 1])
        n_σ_t = tf.slice(input_i, [0, 1], [-1, 1])

        # Un-normalize the inputs
        Dε = self._UnNormalize(n_Dε, self.norm_params[0])
        σ_t = self._UnNormalize(n_σ_t, self.norm_params[1])
        # Set Dζ to 0 for analytical calculation
        Dζ = tf.zeros_like(Dε)

        E = 200000

        F_tdt_analytic = 0.5 * (σ_t * (E ** -1) * σ_t) + 0.5 * E * ((Dε - Dζ) * (Dε - Dζ + (2 * σ_t) / E))
        n_F_tdt_analytic = self._Normalize(F_tdt_analytic, self.norm_params[3])

        return n_F_tdt_analytic

    def get_stress(self, input_i, solver='network'):
        """
        Compute the derivative of F_tdt with respect to Dε using the central difference method.

        Parameters:
        - input_i: A tensor of shape (batch_size, 2), containing [n_Dε, n_σ_t]
        - solver: Method to compute F_tdt, either 'network' (using neural network) or 'analytic' (using analytical equation)

        Returns:
        - n_σ_tdt: The normalized stress at t+dt
        """
        n_Dε = tf.slice(input_i, [0, 0], [-1, 1])
        n_σ_t = tf.slice(input_i, [0, 1], [-1, 1])

        Dε = self._UnNormalize(n_Dε, self.norm_params[0])
        local_delta = tf.maximum(1e-16, 1e-6 * tf.abs(Dε))

        Dε_plus = tf.add(Dε, local_delta)
        Dε_minus = tf.add(Dε, -local_delta)

        n_Dε_plus = self._Normalize(Dε_plus, self.norm_params[0])
        n_Dε_minus = self._Normalize(Dε_minus, self.norm_params[0])

        if solver.lower() == 'network':
            n_F_plus = self.call(tf.concat([n_Dε_plus, n_σ_t], axis=1))
            n_F_minus = self.call(tf.concat([n_Dε_minus, n_σ_t], axis=1))
        elif solver.lower() == 'analytic':
            n_F_plus = self.get_analytic_F(tf.concat([n_Dε_plus, n_σ_t], axis=1))
            n_F_minus = self.get_analytic_F(tf.concat([n_Dε_minus, n_σ_t], axis=1))

        F_plus = self._UnNormalize(n_F_plus, self.norm_params[3])
        F_minus = self._UnNormalize(n_F_minus, self.norm_params[3])

        # Calculate stress using central difference
        σ_tdt = tf.divide(tf.subtract(F_plus, F_minus), 2*local_delta)
        n_σ_tdt = self._Normalize(σ_tdt, self.norm_params[1])

        return n_σ_tdt

    def infer(self, input_test, true_data=False, solver='network'):
        """
        Perform inference on a sequence of inputs.

        Parameters:
        - input_test: Input data with shape (num_samples, num_features)
        - true_data: If True, use true stress values for next step; if False, use predicted stress
        - solver: Method to compute F_tdt, either 'network' (using neural network) or 'analytic' (using analytical equation)

        Returns:
        - pred_F: Predicted free energy values
        - pred_σ: Predicted stress values
        """
        # Extract and normalize inputs
        Dε_full = tf.slice(input_test, [0, 0], [-1, 1])
        σ_t_full = tf.slice(input_test, [0, 1], [-1, 1])

        n_Dε_full = self._Normalize(Dε_full, self.norm_params[0])
        n_σ_t_full = self._Normalize(σ_t_full, self.norm_params[1])

        n_σ_t = n_σ_t_full[0, None]
        num_test = len(n_Dε_full)
        pred_F = np.empty((num_test, 1), dtype=np.float64)
        pred_σ = np.empty((num_test, 1), dtype=np.float64)

        # Perform inference for each time step
        for i in range(num_test):
            input_i = tf.concat([n_Dε_full[i, None], n_σ_t], axis=1)

            if solver.lower() == 'network':
                pred_F[i, 0] = self.call(input_i).numpy()[0, 0]
                pred_σ[i, 0] = self.get_stress(input_i, solver='network').numpy()[0, 0]
            elif solver.lower() == 'analytic':
                pred_F[i, 0] = self.get_analytic_F(input_i).numpy()[0, 0]
                pred_σ[i, 0] = self.get_stress(input_i, solver='analytic').numpy()[0, 0]

            # Update stress for next step
            if not true_data:
                n_σ_t = tf.cast(tf.reshape(pred_σ[i, 0], (1, 1)), GLOBAL_DTYPE)
            else:
                n_σ_t = n_σ_t_full[i+1, None] if i < num_test-1 else n_σ_t_full[i, None]

        # Un-normalize predictions
        pred_F_unnorm = self._UnNormalize(pred_F, self.norm_params[3])
        pred_σ_unnorm = self._UnNormalize(pred_σ, self.norm_params[1])

        return pred_F_unnorm, pred_σ_unnorm

    @classmethod
    def train_model(cls, model_instance, train_data, val_data, LearningRate, nEpochs, bSize, silent_training=False, early_stopping=None, lr_schedule_type='exponential'):
        """
        Train the model with learning rate scheduling.

        Parameters:
        - model_instance: Instance of NNf_TwoInputs to train
        - train_data: Training data with shape (num_samples, num_features)
        - val_data: Validation data with shape (num_samples, num_features)
        - LearningRate: Initial learning rate for the optimizer
        - nEpochs: Number of epochs to train
        - bSize: Batch size
        - silent_training: If True, suppress training output
        - early_stopping: Early stopping callback
        - lr_schedule_type: Type of learning rate schedule to use ('exponential', 'cosine', or 'constant')

        Returns:
        - history: Training history
        """
        silent_verbose = 0 if silent_training else 1
        if early_stopping is None:
            early_stopping = tf.keras.callbacks.EarlyStopping(monitor='val_loss', patience=750, restore_best_weights=True)

        callbacks = [early_stopping]

        if lr_schedule_type == 'exponential':
            # Exponential decay: decay to 1% of initial LR over the training period
            decay_steps = nEpochs // 3  # Decay significantly over 1/3 of training
            lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(
                initial_learning_rate=LearningRate,
                decay_steps=decay_steps,
                decay_rate=0.5,  # Halve the learning rate every decay_steps
                staircase=False  # Smooth decay
            )
            print(f"Using exponential learning rate decay: initial={LearningRate}, decay_steps={decay_steps}, decay_rate=0.5")

        elif lr_schedule_type == 'cosine':
            # Cosine decay with restarts
            first_decay_steps = nEpochs // 5  # First cycle length
            lr_schedule = tf.keras.optimizers.schedules.CosineDecayRestarts(
                initial_learning_rate=LearningRate,
                first_decay_steps=first_decay_steps,
                t_mul=2.0,  # Each cycle gets longer
                m_mul=0.9,  # Each restart has lower max learning rate
                alpha=1e-5   # Minimum learning rate
            )
            print(f"Using cosine decay with restarts: initial={LearningRate}, first_decay_steps={first_decay_steps}")

        else:  # 'constant' or any other value
            lr_schedule = LearningRate
            print(f"Using constant learning rate: {LearningRate}")

        # Add LearningRateScheduler callback to log the learning rate
        class LRLogger(tf.keras.callbacks.Callback):
            def on_epoch_end(self, epoch, logs=None):
                if not silent_training and epoch % 100 == 0:
                    lr = self.model.optimizer.learning_rate
                    if hasattr(lr, '__call__'):
                        print(f"\nEpoch {epoch}: Learning rate: {lr(epoch).numpy():.6f}")
                    else:
                        print(f"\nEpoch {epoch}: Learning rate: {float(lr):.6f}")

        if not silent_training:
            callbacks.append(LRLogger())

        # Create optimizer with the learning rate schedule
        optimizer = tf.keras.optimizers.Nadam(learning_rate=lr_schedule)
        model_instance.compile(optimizer=optimizer, loss=['mae'], metrics=['mape'])

        # Use only the first two columns as inputs (Dε and σ_t)
        history = model_instance.fit(x=train_data[:, 0:2], y=train_data[:, 3],
                                     validation_data=(val_data[:, 0:2], val_data[:, 3]),
                                     epochs=nEpochs, batch_size=bSize,
                                     verbose=silent_verbose, callbacks=callbacks)

        if not silent_verbose:
            print("\n...Training completed in", len(history.history['loss']), "epochs.")

        return history

    @classmethod
    def save_model(cls, model_instance, dataset_name=None, learning_rate=None, num_epochs=None, batch_size=None,
                   norm_params=None, save_dir='saved_models'):
        """
        Save the trained model and its metadata.

        Parameters:
        - model_instance: The trained model instance to be saved
        - dataset_name: Name of the dataset used for training
        - learning_rate: Learning rate used for training
        - num_epochs: Number of epochs used for training
        - batch_size: Batch size used for training
        - norm_params: Normalization parameters
        - save_dir: Directory to save the model

        Returns:
        - model_folder_path: Path to the saved model folder
        """
        # Add "_2inputs" suffix to the folder name
        folder_name = super()._generate_model_folder_name(
            dataset_name=dataset_name, learning_rate=learning_rate, num_epochs=num_epochs, batch_size=batch_size,
            num_hidden_layers=len(model_instance.hidden_layers),
            num_units_per_layer=[layer.units for layer in model_instance.hidden_layers] + [1],
            activation=model_instance.activation_func
        )
        folder_name += "_2inputs"

        model_folder_path = os.path.join(save_dir, folder_name)
        os.makedirs(model_folder_path, exist_ok=True)

        # Save the model
        model_path = os.path.join(model_folder_path, 'model')
        model_instance.save(model_path, save_format='tf')

        layers_config, custom_activation_details = super()._extract_layer_details(model_instance)

        super()._save_metadata(
            model_folder_path=model_folder_path,
            dataset_name=dataset_name,
            learning_rate=learning_rate,
            num_epochs=num_epochs,
            batch_size=batch_size,
            layers_config=layers_config,
            custom_activation_details=custom_activation_details,
            norm_params=norm_params
        )

        # Add additional metadata about this being a two-input model
        metadata_path = os.path.join(model_folder_path, 'metadata.json')
        with open(metadata_path, 'r') as f:
            metadata = json.load(f)

        metadata['Model Type'] = 'Two-Input Model (delta_strain and stress_t only)'
        metadata['Inputs'] = ['delta_strain', 'stress_t']

        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=4)

        print(f"Model saved to {model_folder_path}")
        return model_folder_path

    @classmethod
    def load_model_(cls, model_dir):
        """
        Load a previously saved two-input model.

        Parameters:
        - model_dir: Directory where the model is saved

        Returns:
        - instance: Loaded model instance
        - metadata: Model metadata
        """
        metadata = super()._load_metadata(model_dir)

        norm_params = metadata.get('Normalizing Parameters', None)
        if norm_params is None:
            raise ValueError("Normalization parameters are missing in the model metadata.")

        hidden_dims = [layer['units'] for layer in metadata['Layers'] if layer['units'] != 1]

        # Extract activation function
        if metadata['Custom Activation']:
            activation_name = metadata['Custom Activation']['name']
        else:
            activation_name = metadata['Layers'][0]['activation']

        activation_func = activation_dict[activation_name]

        # Create a new instance
        instance = cls(norm_params=norm_params, hidden_dims=hidden_dims, activation_func=activation_func)

        # Load weights
        model_path = os.path.join(model_dir, 'model')
        loaded_model = tf.keras.models.load_model(model_path, custom_objects={'custom_act': custom_act})
        instance.set_weights(loaded_model.get_weights())

        return instance, metadata

    def get_config(self):
        config = super().get_config()
        config.update({
            'norm_params': self.norm_params,
            'hidden_dims': [layer.units for layer in self.hidden_layers],
            'activation_func': self.activation_func,
        })
        return config

    @classmethod
    def from_config(cls, config):
        return cls(**config)

#%% Debugging Methods
    def debug_single_prediction(self, input_sample_dict, solver='network', log_level=logging.DEBUG, console_output=True):
        """Debug a single prediction to isolate error sources for the two-input model.

        Args:
            input_sample_dict: Dictionary containing sample index and input data with shape [1, 5] containing [Dε, σ_t, Dζ, F_true, σ_true]
            solver: Method to compute F_tdt, either 'network' (using neural network) or 'analytic' (using analytical equation)
            log_level: Logging level for this debug session
            console_output: Whether to output logs to console
        """
        # Setup logger with specified console output preference
        self.setup_logger(log_level=log_level, console_output=console_output)
        logger = logging.getLogger('tann_debug')
        sample_index, input_sample = input_sample_dict.values()

        # Extract and normalize inputs (only use Dε and σ_t)
        n_Dε = self._Normalize(input_sample[0:1, 0:1], self.norm_params[0])
        n_σ_t = self._Normalize(input_sample[0:1, 1:2], self.norm_params[1])
        true_F = input_sample[0, 3]
        true_σ = input_sample[0, 4]

        # Create input tensor with only two inputs
        input_i = tf.concat([n_Dε, n_σ_t], axis=1)

        # Get predictions based on the selected solver
        if solver.lower() == 'network':
            n_F_pred = self.call(input_i)
        elif solver.lower() == 'analytic':
            n_F_pred = self.get_analytic_F(input_i)
        else:
            raise ValueError(f"Unknown solver: {solver}. Use 'network' or 'analytic'.")

        # Un-normalize predictions
        F_pred = self._UnNormalize(n_F_pred, self.norm_params[3])

        # Calculate stress using the selected solver
        n_σ_pred = self.get_stress(input_i, solver=solver)
        σ_pred = self._UnNormalize(n_σ_pred, self.norm_params[1])

        # Calculate errors
        F_error = F_pred.numpy()[0, 0] - true_F
        F_error_percent = (F_error / true_F) * 100 if true_F != 0 else float('inf')
        σ_error = σ_pred.numpy()[0, 0] - true_σ
        σ_error_percent = (σ_error / true_σ) * 100 if true_σ != 0 else float('inf')

        # Log results
        logger.log(log_level, f"===== SINGLE PREDICTION DEBUG (TWO-INPUT MODEL) =====")
        logger.log(log_level, f"Sample index: {sample_index}")
        logger.log(log_level, f"Solver: {solver}")
        logger.log(log_level, f"Inputs: Dε={input_sample[0, 0]:.6f}, σ_t={input_sample[0, 1]:.6f}")
        logger.log(log_level, f"Predictions: F={F_pred.numpy()[0, 0]:.6f}, σ={σ_pred.numpy()[0, 0]:.6f}")
        logger.log(log_level, f"True values: F={true_F:.6f}, σ={true_σ:.6f}")
        logger.log(log_level, f"Errors: F={F_error:.6f} ({F_error_percent:.2f}%), σ={σ_error:.6f} ({σ_error_percent:.2f}%)")

        # Test central difference with different delta values
        logger.log(log_level, "\nTesting central difference approximation with different delta values:")
        Dε_value = input_sample[0, 0]
        deltas = [1e-3, 1e-4, 1e-5, 1e-6, 1e-7, 1e-8]
        cd_results = []

        for delta in deltas:
            local_delta = delta * abs(Dε_value) if abs(Dε_value) > 1e-10 else delta

            # Manual central difference calculation
            Dε_plus = Dε_value + local_delta
            Dε_minus = Dε_value - local_delta

            n_Dε_plus = self._Normalize([[Dε_plus]], self.norm_params[0])
            n_Dε_minus = self._Normalize([[Dε_minus]], self.norm_params[0])

            # Create input tensors with only two inputs
            input_plus = tf.concat([n_Dε_plus, n_σ_t], axis=1)
            input_minus = tf.concat([n_Dε_minus, n_σ_t], axis=1)

            # Calculate F_tdt based on the selected solver
            if solver.lower() == 'network':
                n_F_plus = self.call(input_plus)
                n_F_minus = self.call(input_minus)
            elif solver.lower() == 'analytic':
                n_F_plus = self.get_analytic_F(input_plus)
                n_F_minus = self.get_analytic_F(input_minus)

            # Un-normalize predictions
            F_plus = self._UnNormalize(n_F_plus, self.norm_params[3]).numpy()[0, 0]
            F_minus = self._UnNormalize(n_F_minus, self.norm_params[3]).numpy()[0, 0]

            # Calculate stress using central difference
            σ_cd = (F_plus - F_minus) / (2 * local_delta)
            error = abs(σ_cd - true_σ)

            cd_results.append((delta, σ_cd, error))

        logger.log(log_level, f"Central difference results with different delta values (using {solver} solver):")
        logger.log(log_level, "Delta\tσ_cd\t\tError")
        for delta, σ_cd, error in cd_results:
            logger.log(log_level, f"{delta:.1e}\t{σ_cd:.6f}\t{error:.6f}")
        logger.log(log_level, "====================\n")

        return {
            'solver': solver,
            'inputs': {'Dε': Dε_value, 'σ_t': self._UnNormalize(n_σ_t, self.norm_params[1]).numpy()[0, 0]},
            'predictions': {'F': F_pred.numpy()[0, 0], 'σ': σ_pred.numpy()[0, 0]},
            'true_values': {'F': true_F, 'σ': true_σ},
            'errors': {'F': F_error, 'F_percent': F_error_percent, 'σ': σ_error, 'σ_percent': σ_error_percent},
            'cd_results': cd_results
        }

    def debug_sequential_predictions(self, input_samples, use_true_stress=False, solver='network', log_level=logging.DEBUG, console_output=True):
        """Debug a sequence of predictions to track error accumulation for the two-input model.

        Args:
            input_samples: Input data with shape [n_samples, 5] where each row contains [Dε, σ_t, Dζ, F_true, σ_true]
            use_true_stress: If True, use the true stress values for each step instead of predicted ones
            solver: Method to compute F_tdt, either 'network' (using neural network) or 'analytic' (using analytical equation)
            log_level: Logging level for this debug session
            console_output: Whether to output logs to console
        """
        # Setup logger with specified console output preference
        self.setup_logger(log_level=log_level, console_output=console_output)
        logger = logging.getLogger('tann_debug')

        num_samples = input_samples.shape[0]
        logger.log(log_level, f"===== SEQUENTIAL PREDICTIONS DEBUG (TWO-INPUT MODEL) =====")
        logger.log(log_level, f"Number of samples: {num_samples}")
        logger.log(log_level, f"Solver: {solver}")
        logger.log(log_level, f"Using true stress: {use_true_stress}")

        # Initialize arrays to store results
        pred_F = np.zeros((num_samples, 1))
        pred_σ = np.zeros((num_samples, 1))
        true_F = input_samples[:, 3:4]
        true_σ = input_samples[:, 4:5]

        n_σ_t = self._Normalize(input_samples[0:1, 1:2], self.norm_params[1])

        for i in range(num_samples):
            # Extract and normalize current step inputs (only use Dε and σ_t)
            n_Dε = self._Normalize(input_samples[i:i+1, 0:1], self.norm_params[0])

            # Create input tensor with only two inputs
            input_i = tf.concat([n_Dε, n_σ_t], axis=1)

            # Calculate F_tdt based on the selected solver
            if solver.lower() == 'network':
                n_F_pred = self.call(input_i)
            elif solver.lower() == 'analytic':
                n_F_pred = self.get_analytic_F(input_i)
            else:
                raise ValueError(f"Unknown solver: {solver}. Use 'network' or 'analytic'.")

            # Calculate stress using the selected solver
            n_σ_pred = self.get_stress(input_i, solver=solver)

            # Un-normalize predictions
            F_pred_value = self._UnNormalize(n_F_pred, self.norm_params[3]).numpy()[0, 0]
            σ_pred_value = self._UnNormalize(n_σ_pred, self.norm_params[1]).numpy()[0, 0]

            # Store predictions
            pred_F[i, 0] = F_pred_value
            pred_σ[i, 0] = σ_pred_value

            if i % 5 == 0 or i == num_samples - 1:
                logger.log(log_level, f"Step {i+1}/{num_samples}:")
                logger.log(log_level, f"  Inputs: Dε={input_samples[i, 0]:.6f}, σ_t={self._UnNormalize(n_σ_t, self.norm_params[1]).numpy()[0, 0]:.6f}")
                logger.log(log_level, f"  Predictions: F={F_pred_value:.6f}, σ={σ_pred_value:.6f}")
                logger.log(log_level, f"  True values: F={input_samples[i, 3]:.6f}, σ={input_samples[i, 4]:.6f}")
                F_error = F_pred_value - input_samples[i, 3]
                F_error_percent = (F_error / input_samples[i, 3]) * 100 if input_samples[i, 3] != 0 else float('inf')
                σ_error = σ_pred_value - input_samples[i, 4]
                σ_error_percent = (σ_error / input_samples[i, 4]) * 100 if input_samples[i, 4] != 0 else float('inf')
                logger.log(log_level, f"  Errors: F={F_error:.6f} ({F_error_percent:.2f}%), σ={σ_error:.6f} ({σ_error_percent:.2f}%)")

            # Update stress for next step
            if i < num_samples - 1:  # Only update if not the last step
                if use_true_stress:
                    n_σ_t = self._Normalize(input_samples[i+1:i+2, 1:2], self.norm_params[1])
                else:
                    n_σ_t = n_σ_pred

        # Calculate errors
        F_errors = pred_F - true_F
        F_errors_percent = np.zeros_like(F_errors)
        mask = true_F != 0
        F_errors_percent[mask] = (F_errors[mask] / true_F[mask]) * 100

        σ_errors = pred_σ - true_σ
        σ_errors_percent = np.zeros_like(σ_errors)
        mask = true_σ != 0
        σ_errors_percent[mask] = (σ_errors[mask] / true_σ[mask]) * 100

        # Log summary statistics
        logger.log(log_level, "\n===== ERROR SUMMARY =====")
        logger.log(log_level, f"Average absolute F error: {np.mean(np.abs(F_errors)):.6f}")
        logger.log(log_level, f"Average absolute σ error: {np.mean(np.abs(σ_errors)):.6f}")
        logger.log(log_level, f"Average absolute F error (%): {np.mean(np.abs(F_errors_percent[mask])):.2f}%")
        logger.log(log_level, f"Average absolute σ error (%): {np.mean(np.abs(σ_errors_percent[mask])):.2f}%")
        logger.log(log_level, f"Max absolute F error: {np.max(np.abs(F_errors)):.6f} at step {np.argmax(np.abs(F_errors))+1}")
        logger.log(log_level, f"Max absolute σ error: {np.max(np.abs(σ_errors)):.6f} at step {np.argmax(np.abs(σ_errors))+1}")

        return {
            'solver': solver,
            'use_true_stress': use_true_stress,
            'predictions': {
                'F': pred_F.flatten().tolist(),
                'σ': pred_σ.flatten().tolist()
            },
            'true_values': {
                'F': true_F.flatten().tolist(),
                'σ': true_σ.flatten().tolist()
            },
            'errors': {
                'F': F_errors.flatten().tolist(),
                'F_percent': F_errors_percent.flatten().tolist(),
                'σ': σ_errors.flatten().tolist(),
                'σ_percent': σ_errors_percent.flatten().tolist(),
                'avg_abs_F_error': float(np.mean(np.abs(F_errors))),
                'avg_abs_σ_error': float(np.mean(np.abs(σ_errors))),
                'avg_abs_F_error_percent': float(np.mean(np.abs(F_errors_percent[mask]))),
                'avg_abs_σ_error_percent': float(np.mean(np.abs(σ_errors_percent[mask]))),
                'max_abs_F_error': float(np.max(np.abs(F_errors))),
                'max_abs_σ_error': float(np.max(np.abs(σ_errors)))
            },
            'summary': {
                'pred_F': pred_F.flatten().tolist(),
                'pred_σ': pred_σ.flatten().tolist(),
                'true_F': true_F.flatten().tolist(),
                'true_σ': true_σ.flatten().tolist()
            }
        }

    def test_controlled_inputs(self, variable_param='strain', constant_values=None, variable_range=None, manual_inputs=None, num_points=50, solver='network', log_level=logging.DEBUG, console_output=True, plot_results=True):
        """Test the two-input network with controlled inputs to isolate network behavior.

        This function tests the network with controlled inputs, with several options:
        1. Vary strain (Dε) while keeping stress (σ_t) constant
        2. Vary stress (σ_t) while keeping strain (Dε) constant
        3. Provide manual inputs for both strain and stress

        Args:
            variable_param: Which parameter to vary - 'strain', 'stress', or 'manual'
            constant_values: Dictionary of constant values, e.g., {'stress': 100.0} or {'strain': 0.001}
            variable_range: Tuple of (min_value, max_value) for the variable parameter
            manual_inputs: Dictionary with arrays of strain and stress values when variable_param='manual'
                          e.g., {'strain': [0.001, 0.002, ...], 'stress': [100.0, 120.0, ...]}
            num_points: Number of test points to generate (not used when variable_param='manual')
            solver: Method to compute F_tdt, either 'network' (using neural network) or 'analytic' (using analytical equation)
            log_level: Logging level for this debug session
            console_output: Whether to output logs to console
            plot_results: Whether to plot the results

        Returns:
            Dictionary containing test inputs, predictions, analytical solutions, and errors
        """
        # Setup logger with specified console output preference
        self.setup_logger(log_level=log_level, console_output=console_output)
        logger = logging.getLogger('tann_debug')

        # Set default values if not provided
        if constant_values is None:
            if variable_param == 'strain':
                constant_values = {'stress': 100.0}
            elif variable_param == 'stress':
                constant_values = {'strain': 0.001}

        # Generate test inputs
        if variable_param == 'manual':
            if manual_inputs is None or 'strain' not in manual_inputs or 'stress' not in manual_inputs:
                raise ValueError("When variable_param='manual', you must provide manual_inputs with 'strain' and 'stress' arrays")

            strain_values = np.array(manual_inputs['strain'])
            stress_values = np.array(manual_inputs['stress'])

            if len(strain_values) != len(stress_values):
                raise ValueError("The strain and stress arrays must have the same length")

            num_points = len(strain_values)
            variable_values = np.arange(num_points)  # Just use indices for plotting

            logger.log(log_level, f"===== CONTROLLED INPUT TEST (TWO-INPUT MODEL) =====")
            logger.log(log_level, f"Variable parameter: {variable_param} (using manual inputs)")
            logger.log(log_level, f"Number of test points: {num_points}")
            logger.log(log_level, f"Using {solver} solver for network predictions")
        else:
            min_val, max_val = variable_range
            variable_values = np.linspace(min_val, max_val, num_points)

            logger.log(log_level, f"===== CONTROLLED INPUT TEST (TWO-INPUT MODEL) =====")
            logger.log(log_level, f"Variable parameter: {variable_param}")
            logger.log(log_level, f"Constant values: {constant_values}")
            logger.log(log_level, f"Testing {variable_param} range: [{min_val}, {max_val}]")
            logger.log(log_level, f"Using {solver} solver for network predictions")

        pred_F = np.zeros((num_points, 1))
        pred_σ = np.zeros((num_points, 1))
        analytic_F = np.zeros((num_points, 1))
        analytic_σ = np.zeros((num_points, 1))

        strain_inputs = np.zeros(num_points)
        stress_inputs = np.zeros(num_points)

        E = 200000

        for i in range(num_points):
            if variable_param == 'strain':
                Dε = variable_values[i]
                σ_t = constant_values['stress']
            elif variable_param == 'stress':
                Dε = constant_values['strain']
                σ_t = variable_values[i]
            else:  # variable_param == 'manual'
                Dε = strain_values[i]
                σ_t = stress_values[i]

            strain_inputs[i] = Dε
            stress_inputs[i] = σ_t

            Dζ = 0.0  # Always zero for the two-input model
            F_analytic = 0.5 * (σ_t * (E ** -1) * σ_t) + 0.5 * E * ((Dε - Dζ) * (Dε - Dζ + (2 * σ_t) / E))
            σ_analytic = σ_t + E * (Dε - Dζ)

            analytic_F[i, 0] = F_analytic
            analytic_σ[i, 0] = σ_analytic

            n_Dε = self._Normalize(np.array([[Dε]]), self.norm_params[0])
            n_σ_t = self._Normalize(np.array([[σ_t]]), self.norm_params[1])
            input_i = tf.concat([n_Dε, n_σ_t], axis=1)

            if solver.lower() == 'network':
                n_F_pred = self.call(input_i)
            elif solver.lower() == 'analytic':
                n_F_pred = self.get_analytic_F(input_i)
            else:
                raise ValueError(f"Unknown solver: {solver}. Use 'network' or 'analytic'.")

            F_pred_value = self._UnNormalize(n_F_pred, self.norm_params[3]).numpy()[0, 0]

            # Calculate stress using the selected solver
            n_σ_pred = self.get_stress(input_i, solver=solver)
            σ_pred_value = self._UnNormalize(n_σ_pred, self.norm_params[1]).numpy()[0, 0]

            # Store predictions
            pred_F[i, 0] = F_pred_value
            pred_σ[i, 0] = σ_pred_value

            # Log every 10th point
            if i % 5 == 0 or i == num_points - 1:
                if variable_param == 'strain':
                    logger.log(log_level, f"Point {i+1}/{num_points}: Dε={Dε:.6f}, σ_t={constant_values['stress']:.6f}")
                elif variable_param == 'stress':
                    logger.log(log_level, f"Point {i+1}/{num_points}: Dε={constant_values['strain']:.6f}, σ_t={σ_t:.6f}")
                else:  # variable_param == 'manual'
                    logger.log(log_level, f"Point {i+1}/{num_points}: Dε={Dε:.6f}, σ_t={σ_t:.6f}")
                logger.log(log_level, f"  F: Predicted={F_pred_value:.6f}, Analytical={F_analytic:.6f}, Error={(F_pred_value-F_analytic)/F_analytic*100:.2f}%")
                logger.log(log_level, f"  σ: Predicted={σ_pred_value:.6f}, Analytical={σ_analytic:.6f}, Error={(σ_pred_value-σ_analytic)/σ_analytic*100:.2f}%")

        # Calculate errors
        F_errors_dict ={
            'absolute': (pred_F - analytic_F),
            'percent': (pred_F - analytic_F) / analytic_F * 100
        }
        σ_errors_dict ={
            'absolute': (pred_σ - analytic_σ),
            'percent': (pred_σ - analytic_σ) / analytic_σ * 100
        }
        #TODO: Determine which error type to use
        F_errors = F_errors_dict['absolute']
        σ_errors = σ_errors_dict['absolute']

        # Log summary statistics
        logger.log(log_level, "===== ERROR SUMMARY =====")
        logger.log(log_level, f"Average F error: {np.mean(np.abs(F_errors)):.2f}")
        logger.log(log_level, f"Average σ error: {np.mean(np.abs(σ_errors)):.2f}")

        if variable_param == 'strain':
            logger.log(log_level, f"Max F error: {np.max(np.abs(F_errors)):.2f} at Dε={variable_values[np.argmax(np.abs(F_errors))]}")
            logger.log(log_level, f"Max σ error: {np.max(np.abs(σ_errors)):.2f} at Dε={variable_values[np.argmax(np.abs(σ_errors))]}")
        elif variable_param == 'stress':
            logger.log(log_level, f"Max F error: {np.max(np.abs(F_errors)):.2f} at σ_t={variable_values[np.argmax(np.abs(F_errors))]}")
            logger.log(log_level, f"Max σ error: {np.max(np.abs(σ_errors)):.2f} at σ_t={variable_values[np.argmax(np.abs(σ_errors))]}")
        else:  # variable_param == 'manual'
            logger.log(log_level, f"Max F error: {np.max(np.abs(F_errors)):.2f} at point {np.argmax(np.abs(F_errors))+1}")
            logger.log(log_level, f"Max σ error: {np.max(np.abs(σ_errors)):.2f} at point {np.argmax(np.abs(σ_errors))+1}")

        # Plot results if requested
        if plot_results:
            fig_path = self._plot_controlled_test_results_two_inputs(variable_values, variable_param, pred_F, pred_σ, analytic_F, analytic_σ, F_errors, σ_errors, solver)
        else:
            fig_path = None

        # Prepare return dictionary
        result_dict = {
            'variable_param': variable_param,
            'variable_values': variable_values.tolist(),
            'constant_values': constant_values,
            'inputs': {
                'strain': strain_inputs.tolist(),
                'stress': stress_inputs.tolist(),
            },
            'predictions': {
                'F': pred_F.flatten().tolist(),
                'σ': pred_σ.flatten().tolist()
            },
            'analytical': {
                'F': analytic_F.flatten().tolist(),
                'σ': analytic_σ.flatten().tolist()
            },
            'errors': {
                'F': F_errors.flatten().tolist(),
                'σ': σ_errors.flatten().tolist(),
                'avg_F_error': float(np.mean(np.abs(F_errors))),
                'avg_σ_error': float(np.mean(np.abs(σ_errors))),
                'max_F_error': float(np.max(np.abs(F_errors))),
                'max_σ_error': float(np.max(np.abs(σ_errors)))
            },
            'fig_path': fig_path
        }

        return result_dict

    def _plot_controlled_test_results_two_inputs(self, variable_values, variable_param, pred_F, pred_σ, analytic_F, analytic_σ, F_errors, σ_errors, solver):
        """Plot the results of the controlled input test for the two-input model.

        Args:
            variable_values: Array of variable parameter values (either Dε, σ_t, or indices for manual mode)
            variable_param: Which parameter is variable ('strain', 'stress', or 'manual')
            pred_F: Array of predicted F values
            pred_σ: Array of predicted σ values
            analytic_F: Array of analytical F values
            analytic_σ: Array of analytical σ values
            F_errors: Array of F errors (percent)
            σ_errors: Array of σ errors (percent)
            solver: Solver used for predictions ('network' or 'analytic')

        Returns:
            Path to the saved figure
        """
        plt.rcParams["font.family"] = "serif"
        plt.rc('axes.formatter', use_mathtext=True)
        plt.rcParams["font.serif"] = "cmr10"
        plt.rcParams['font.size']=12
        # Create directory if it doesn't exist
        save_dir = 'logs'
        os.makedirs(save_dir, exist_ok=True)

        # Use the same timestamp as the log file
        if not hasattr(self, '_debug_timestamp') or not self._debug_timestamp:
            self._debug_timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

        # Create figure with 4 subplots
        _, axs = plt.subplots(2, 2, figsize=(12, 10))

        # Set x-axis label based on variable parameter
        if variable_param == 'strain':
            x_label = r'$\Delta\varepsilon$'
        elif variable_param == 'stress':
            x_label = r'$\sigma_t$'
        else:  # variable_param == 'manual'
            x_label = 'Test Point Index'

        # Plot F values
        axs[0, 0].plot(variable_values, pred_F, marker='o', linewidth=0, label=f'Predicted ({solver})')
        axs[0, 0].plot(variable_values, analytic_F, marker='x', linewidth=0, markersize=8, label='Analytical')
        axs[0, 0].set_xlabel(x_label)
        axs[0, 0].set_ylabel(r'$F_{t+1}$')
        axs[0, 0].legend(); axs[0, 0].ticklabel_format(style='sci', axis='both', scilimits=(0, 2))
        axs[0, 0].grid(True, linestyle='--', alpha=0.5)

        # Plot σ values
        axs[0, 1].plot(variable_values, pred_σ, marker='o', linewidth=0, label=f'Predicted ({solver})')
        axs[0, 1].plot(variable_values, analytic_σ, marker='x', linewidth=0, label='Analytical')
        axs[0, 1].set_xlabel(x_label)
        axs[0, 1].set_ylabel(r'$\sigma_{t+1}$')
        axs[0, 1].legend(); axs[0, 1].ticklabel_format(style='sci', axis='both', scilimits=(0, 2))
        axs[0, 1].grid(True, linestyle='--', alpha=0.5)

        # Plot F errors
        axs[1, 0].plot(variable_values, F_errors, 'r-')
        axs[1, 0].set_title('Free Energy Error')
        axs[1, 0].set_xlabel(x_label)
        axs[1, 0].set_ylabel('Error'); axs[1, 0].ticklabel_format(style='sci', axis='both', scilimits=(0, 2))
        axs[1, 0].grid(True, linestyle='--', alpha=0.5)

        # Plot σ errors
        axs[1, 1].plot(variable_values, σ_errors, 'r-')
        axs[1, 1].set_title('Stress Error')
        axs[1, 1].set_xlabel(x_label)
        axs[1, 1].set_ylabel('Error'); axs[1, 1].ticklabel_format(style='sci', axis='both', scilimits=(0, 2))
        axs[1, 1].grid(True, linestyle='--', alpha=0.5)

        plt.tight_layout()

        # Save the figure
        fig_path = os.path.join(save_dir, f'two_input_controlled_test_{self._debug_timestamp}.png')
        plt.savefig(fig_path, dpi=300, bbox_inches='tight')

        logger = logging.getLogger('tann_debug')
        logger.info(f"Two-input controlled test plot saved to {fig_path}")

        plt.show()

        return fig_path
