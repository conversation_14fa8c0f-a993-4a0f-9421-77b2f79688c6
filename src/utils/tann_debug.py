"""
Mixin class providing debugging functionality for TANN models.

    This mixin provides methods for debugging single predictions and sequential
    predictions in TANN models.
    It also includes methods to assess model's performance with controlled inputs.
    It also includes plotting utilities for visualizing the results.

    NOTE: It assumes the host class (in this repo "NNf")has the following:
    - `_Normalize` and `_UnNormalize` methods
    - `norm_params` attribute
    - `call` method for forward pass
    - `get_stress` method

Date: 2025-03-04
Author: <PERSON>rez<PERSON>ahnej<PERSON>
"""
#%% ---- Import Required Libraries ----
import logging
import os
from datetime import datetime

import matplotlib.pyplot as plt
import numpy as np
import tensorflow as tf

#%% ---- Set up Logger ----
logger = logging.getLogger('tann_debug')
logger.setLevel(logging.DEBUG)

#%% ---- Debugging Class ----
class NNfDebugClass:
    def setup_logger(self, log_dir='logs', log_level=logging.DEBUG, console_output=True):
        """Set up a logger for debugging output.

        Args:
            log_dir: Directory to store log files
            log_level: Logging level (default: DEBUG)
            console_output: Whether to output logs to console (default: True)
        """
        logger = logging.getLogger('tann_debug')

        # Remove any existing handlers to avoid duplicate logging
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # Set the logging level
        logger.setLevel(log_level)

        # Create directory
        os.makedirs(log_dir, exist_ok=True)
        self._debug_timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        self._log_file = os.path.join(log_dir, f'tann_debug_{self._debug_timestamp}.log')

        # Create formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        # Create file handler
        file_handler = logging.FileHandler(self._log_file)
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        # Create console handler if requested
        if console_output:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(log_level)
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)

        logger.info(f"Logger initialized. Logs will be written to {self._log_file}")
        return logger

    def debug_single_prediction(self, input_sample_dict, solver='network', log_level=logging.DEBUG, console_output=True):
        """Debug a single prediction to isolate error sources. The aim here is to validate the
        Central Finite Difference Method that is used to calculate the stress by taking the
        derivative of the predicted free energy with respect to strain increment.

        Args:
            input_sample_dict: Dictionary containing sample index and input data with shape [1, 5] containing [Dε, σ_t, Dζ, F_true, σ_true]
            solver: Method to compute F_tdt, either 'network' (using neural network) or 'analytic' (using analytical equation)
            log_level: Logging level for this debug session
            console_output: Whether to output logs to console
        """
        # Setup logger with specified console output preference
        self.setup_logger(log_level=log_level, console_output=console_output)
        sample_index, input_sample = input_sample_dict.values()

        n_Dε = self._Normalize(input_sample[0:1, 0:1], self.norm_params[0])
        n_σ_t = self._Normalize(input_sample[0:1, 1:2], self.norm_params[1])
        n_Dζ = self._Normalize(input_sample[0:1, 2:3], self.norm_params[2])
        true_F = input_sample[0, 3]
        true_σ = input_sample[0, 4]

        input_i = tf.concat([n_Dε, n_σ_t, n_Dζ], axis=1)

        # Get predictions based on the selected solver
        if solver.lower() == 'network':
            n_F_pred = self.call(input_i)
        elif solver.lower() == 'analytic':
            n_F_pred = self.get_analytic_F(input_i)
        else:
            raise ValueError(f"Unknown solver: {solver}. Use 'network' or 'analytic'.")

        F_pred = self._UnNormalize(n_F_pred, self.norm_params[3])

        n_σ_pred = self.get_stress(input_i, solver=solver)
        σ_pred = self._UnNormalize(n_σ_pred, self.norm_params[1])

        # Calculate errors
        F_error = F_pred.numpy()[0, 0] - true_F
        F_error_percent = (F_error / true_F) * 100 if true_F != 0 else float('inf')

        σ_error = σ_pred.numpy()[0, 0] - true_σ
        σ_error_percent = (σ_error / true_σ) * 100 if true_σ != 0 else float('inf')

        # Debug central difference calculation
        n_Dε_value = n_Dε.numpy()[0, 0]
        Dε_value = self._UnNormalize(n_Dε_value, self.norm_params[0])

        # Test different delta values for CD method.
        deltas = [1e-8, 1e-7, 1e-6, 1e-5, 1e-4, 1e-3, 1e-2]
        cd_results = []

        for delta in deltas:
            local_delta = delta * abs(Dε_value) if abs(Dε_value) > 1e-10 else delta

            # Manual central difference calculation
            Dε_plus = Dε_value + local_delta
            Dε_minus = Dε_value - local_delta

            n_Dε_plus = self._Normalize([[Dε_plus]], self.norm_params[0])
            n_Dε_minus = self._Normalize([[Dε_minus]], self.norm_params[0])

            input_plus = tf.concat([n_Dε_plus, n_σ_t, n_Dζ], axis=1)
            input_minus = tf.concat([n_Dε_minus, n_σ_t, n_Dζ], axis=1)

            # Calculate F_tdt based on the selected solver
            if solver.lower() == 'network':
                n_F_plus = self.call(input_plus)
                n_F_minus = self.call(input_minus)
            elif solver.lower() == 'analytic':
                n_F_plus = self.get_analytic_F(input_plus)
                n_F_minus = self.get_analytic_F(input_minus)

            F_plus = self._UnNormalize(n_F_plus, self.norm_params[3]).numpy()[0, 0]
            F_minus = self._UnNormalize(n_F_minus, self.norm_params[3]).numpy()[0, 0]

            σ_cd = (F_plus - F_minus) / (2 * local_delta)
            cd_results.append((delta, σ_cd, abs(σ_cd - true_σ)))

        # Log detailed debug info
        logger.log(log_level, "===== SINGLE PREDICTION DEBUG =====")
        logger.log(log_level, f"Using {solver} solver for F_tdt calculation")
        logger.log(log_level, f"Input sample: {sample_index}")
        logger.log(log_level, f"Input values (unnormalized): Dε={Dε_value}, σ_t={self._UnNormalize(n_σ_t, self.norm_params[1]).numpy()[0, 0]}, Dζ={self._UnNormalize(n_Dζ, self.norm_params[2]).numpy()[0, 0]}")
        logger.log(log_level, f"Input values (normalized): n_Dε={n_Dε.numpy()[0, 0]}, n_σ_t={n_σ_t.numpy()[0, 0]}, n_Dζ={n_Dζ.numpy()[0, 0]}")
        logger.log(log_level, f"F prediction ({solver}): {F_pred.numpy()[0, 0]} (true: {true_F})")
        logger.log(log_level, f"F error: {F_error} ({F_error_percent:.3f}%)")
        logger.log(log_level, f"σ prediction ({solver}): {σ_pred.numpy()[0, 0]} (true: {true_σ})")
        logger.log(log_level, f"σ error: {σ_error} ({σ_error_percent:.3f}%)")

        logger.log(log_level, f"Central difference results with different delta values (using {solver} solver):")
        logger.log(log_level, "Delta\tσ_cd\t\tError")
        for delta, σ_cd, error in cd_results:
            logger.log(log_level, f"{delta:.1e}\t{σ_cd:.6f}\t{error:.6f}")
        logger.log(log_level, "====================\n")
        return {
            'solver': solver,
            'inputs': {'Dε': Dε_value, 'σ_t': self._UnNormalize(n_σ_t, self.norm_params[1]).numpy()[0, 0], 'Dζ': self._UnNormalize(n_Dζ, self.norm_params[2]).numpy()[0, 0]},
            'predictions': {'F': F_pred.numpy()[0, 0], 'σ': σ_pred.numpy()[0, 0]},
            'true_values': {'F': true_F, 'σ': true_σ},
            'errors': {'F': F_error, 'F_percent': F_error_percent, 'σ': σ_error, 'σ_percent': σ_error_percent},
            'cd_results': cd_results
        }

    def debug_sequential_predictions(self, input_samples, use_true_stress=False, solver='network', log_level=logging.DEBUG, console_output=True):
        """Debug a sequence of predictions to track error accumulation

        Args:
            input_samples: Input data with shape [n_samples, 5] where each row contains [Dε, σ_t, Dζ, F_true, σ_true]
            use_true_stress: If True, use the true stress values for each step instead of predicted ones
            solver: Method to compute F_tdt, either 'network' (using neural network) or 'analytic' (using analytical equation)
            log_level: Logging level for this debug session
            console_output: Whether to output logs to console
        """
        # Setup logger with specified console output preference
        self.setup_logger(log_level=log_level, console_output=console_output)

        num_samples = input_samples.shape[0]
        results = []

        pred_F, pred_σ, F_errors, σ_errors = \
            np.zeros((num_samples, 1)), \
            np.zeros((num_samples, 1)), \
            np.zeros((num_samples, 1)), \
            np.zeros((num_samples, 1))

        # Get initial stress
        n_σ_t = self._Normalize(input_samples[0:1, 1:2], self.norm_params[1])

        logger.log(log_level, f"SEQUENTIAL PREDICTION debug with {num_samples} samples")
        logger.log(log_level, f"Using {'true' if use_true_stress else 'predicted'} stress for sequential steps")
        logger.log(log_level, f"Using {solver} solver for F_tdt calculation")

        for i in range(num_samples):
            # Extract and normalize current step inputs
            n_Dε = self._Normalize(input_samples[i:i+1, 0:1], self.norm_params[0])
            n_Dζ = self._Normalize(input_samples[i:i+1, 2:3], self.norm_params[2])
            input_i = tf.concat([n_Dε, n_σ_t, n_Dζ], axis=1)

            true_F = input_samples[i, 3]
            true_σ = input_samples[i, 4]

            # Calculate F_tdt based on the selected solver
            if solver.lower() == 'network':
                n_F_pred = self.call(input_i)
            elif solver.lower() == 'analytic':
                n_F_pred = self.get_analytic_F(input_i)
            else:
                raise ValueError(f"Unknown solver: {solver}. Use 'network' or 'analytic'.")

            F_pred = self._UnNormalize(n_F_pred, self.norm_params[3])

            # Calculate stress using the selected solver
            n_σ_pred = self.get_stress(input_i, solver=solver)
            σ_pred = self._UnNormalize(n_σ_pred, self.norm_params[1])

            # Store predictions
            pred_F[i, 0] = F_pred.numpy()[0, 0]
            pred_σ[i, 0] = σ_pred.numpy()[0, 0]

            # Calculate errors
            F_error = pred_F[i, 0] - true_F
            F_error_percent = (F_error / true_F) * 100 if true_F != 0 else float('inf')

            σ_error = pred_σ[i, 0] - true_σ
            σ_error_percent = (σ_error / true_σ) * 100 if true_σ != 0 else float('inf')

            # Store errors
            F_errors[i, 0] = F_error_percent
            σ_errors[i, 0] = σ_error_percent

            # Log step results
            logger.log(log_level, f"Step {i+1}/{num_samples}:")
            logger.log(log_level, f"Inputs: Dε={input_samples[i, 0]}, σ_t={self._UnNormalize(n_σ_t, self.norm_params[1]).numpy()[0, 0]}, Dζ={input_samples[i, 2]}")
            logger.log(log_level, f"F prediction ({solver}): {pred_F[i, 0]} (true: {true_F}, error: {F_error_percent:.6f}%)")
            logger.log(log_level, f"σ prediction ({solver}): {pred_σ[i, 0]} (true: {true_σ}, error: {σ_error_percent:.6f}%)")

            # Store step results
            step_result = {
                'step': i,
                'solver': solver,
                'inputs': {
                    'Dε': input_samples[i, 0],
                    'σ_t': self._UnNormalize(n_σ_t, self.norm_params[1]).numpy()[0, 0],
                    'Dζ': input_samples[i, 2]
                },
                'predictions': {'F': pred_F[i, 0], 'σ': pred_σ[i, 0]},
                'true_values': {'F': true_F, 'σ': true_σ},
                'errors': {'F': F_error, 'F_percent': F_error_percent, 'σ': σ_error, 'σ_percent': σ_error_percent}
            }
            results.append(step_result)

            # Update stress for next step - either use predicted stress or true stress
            if i < num_samples - 1:  # Only update if not the last sample
                if use_true_stress:
                    n_σ_t = self._Normalize(input_samples[i+1:i+2, 1:2], self.norm_params[1])
                else:
                    n_σ_t = n_σ_pred

        # Log summary
        logger.log(log_level, "===== ERROR SUMMARY =====")
        logger.log(log_level, f"Average F error: {np.mean(np.abs(F_errors)):.2f}%")
        logger.log(log_level, f"Average σ error: {np.mean(np.abs(σ_errors)):.2f}%")
        logger.log(log_level, f"Max F error: {np.max(np.abs(F_errors)):.2f}% at step {np.argmax(np.abs(F_errors))+1}")
        logger.log(log_level, f"Max σ error: {np.max(np.abs(σ_errors)):.2f}% at step {np.argmax(np.abs(σ_errors))+1}")

        # Calculate error accumulation
        logger.log(log_level, "===== ERROR ACCUMULATION ANALYSIS =====")
        if num_samples > 1:
            F_error_trend = np.polyfit(range(num_samples), np.abs(F_errors).flatten(), 1)[0]
            σ_error_trend = np.polyfit(range(num_samples), np.abs(σ_errors).flatten(), 1)[0]
            logger.log(log_level, f"F error trend: {'Increasing' if F_error_trend > 0 else 'Decreasing'} ({F_error_trend:.4f} per step)")
            logger.log(log_level, f"σ error trend: {'Increasing' if σ_error_trend > 0 else 'Decreasing'} ({σ_error_trend:.4f} per step)\n")

        return {
            'step_results': results,
            'summary': {
                'avg_F_error_percent': np.mean(np.abs(F_errors)),
                'avg_σ_error_percent': np.mean(np.abs(σ_errors)),
                'max_F_error_percent': np.max(np.abs(F_errors)),
                'max_σ_error_percent': np.max(np.abs(σ_errors)),
                'F_errors': F_errors.flatten().tolist(),
                'σ_errors': σ_errors.flatten().tolist(),
                'pred_F': pred_F.flatten().tolist(),
                'pred_σ': pred_σ.flatten().tolist(),
                'true_F': input_samples[:, 3].tolist(),
                'true_σ': input_samples[:, 4].tolist()
            }
        }

    def plot_error_propagation(self, seq_result, seq_true_result=None, save_dir='logs', show_plot=True):
        """Plot error propagation for sequential predictions and save figures.

        Args:
            seq_result: Result dictionary from debug_sequential_predictions with use_true_stress=False
            seq_true_result: Optional result dictionary from debug_sequential_predictions with use_true_stress=True
            save_dir: Directory to save the plots (default: 'logs')
            show_plot: Whether to display the plots (default: True)
        """

        # Create directory if it doesn't exist
        os.makedirs(save_dir, exist_ok=True)

        # Use the same timestamp as the log file
        if not self._debug_timestamp:
            self._debug_timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

        plt.figure(figsize=(10, 7.5))

        # Get solver information
        solver = seq_result['step_results'][0]['solver'] if 'solver' in seq_result['step_results'][0] else 'network'
        solver_true = seq_true_result['step_results'][0]['solver'] if seq_true_result and 'solver' in seq_true_result['step_results'][0] else 'network'

        # Free Energy Error subplot
        plt.subplot(2, 1, 1)
        plt.plot(seq_result['summary']['F_errors'], 'r-o', label=f'Using predicted stress ({solver})', markersize=3, markevery=10)
        if seq_true_result:
            plt.plot(seq_true_result['summary']['F_errors'], 'b-o', label=f'Using true stress ({solver_true})', markersize=3, markevery=10)
        plt.title(f'Free Energy Prediction Error ({solver} solver)')
        plt.ylabel('Error (%)')
        plt.xlabel('Step')
        plt.legend(); plt.ticklabel_format(style='sci', axis='both', scilimits=(0, 2))
        plt.grid(True, linestyle='--', alpha=0.5)

        # Stress Error subplot
        plt.subplot(2, 1, 2)
        plt.plot(seq_result['summary']['σ_errors'], 'r-o', label=f'Using predicted stress ({solver})', markersize=3, markevery=10)
        if seq_true_result:
            plt.plot(seq_true_result['summary']['σ_errors'], 'b-o', label=f'Using true stress ({solver_true})', markersize=3, markevery=10)
        plt.title(f'Stress Prediction Error ({solver} solver)')
        plt.ylabel('Error (%)')
        plt.xlabel('Step')
        plt.legend(); plt.ticklabel_format(style='sci', axis='both', scilimits=(0, 2))
        plt.grid(True, linestyle='--', alpha=0.5)

        plt.tight_layout()

        # Save the figure with the same timestamp as the log file
        fig_path = os.path.join(save_dir, f'tann_debug_{self._debug_timestamp}.png')
        plt.savefig(fig_path, dpi=300, bbox_inches='tight')
        logger = logging.getLogger('tann_debug')
        logger.info(f"Error propagation plot saved to {fig_path}")

        if show_plot:
            plt.show()
        else:
            plt.close()

        return fig_path

    def test_controlled_inputs(self, variable_param='strain', constant_values=None, variable_range=None, manual_inputs=None, num_points=50, solver='network', log_level=logging.DEBUG, console_output=True, plot_results=True):
        """Test the network with controlled inputs to isolate network behavior.
        The aim here is to keep one or more inputs constant and vary others in order to understand the effect of
        each input on the predictions.

        This function tests the network with controlled inputs, with several options:
        1. Vary strain (Dε) while keeping stress (σ_t) and Dζ constant
        2. Vary stress (σ_t) while keeping strain (Dε) and Dζ constant
        3. Provide manual inputs for both strain and stress, keeping only Dζ constant

        Args:
            variable_param: Which parameter to vary - 'strain', 'stress', or 'manual'
            constant_values: Dictionary of constant values, e.g., {'stress': 100.0, 'dz': 0.0} or {'strain': 0.001, 'dz': 0.0}
            variable_range: Tuple of (min_value, max_value) for the variable parameter
            manual_inputs: Dictionary with arrays of strain and stress values when variable_param='manual'
                          e.g., {'strain': [0.001, 0.002, ...], 'stress': [100.0, 120.0, ...]}
            num_points: Number of test points to generate (not used when variable_param='manual')
            solver: Method to compute F_tdt, either 'network' (using neural network) or 'analytic' (using analytical equation)
            log_level: Logging level for this debug session
            console_output: Whether to output logs to console
            plot_results: Whether to plot the results

        Returns:
            Dictionary containing test inputs, predictions, analytical solutions, and errors
        """
        # Setup logger with specified console output preference
        self.setup_logger(log_level=log_level, console_output=console_output)
        logger = logging.getLogger('tann_debug')

        # Set default values if not provided
        if constant_values is None:
            if variable_param == 'strain':
                constant_values = {'stress': 100.0, 'dz': 0.0}
            elif variable_param == 'stress':
                constant_values = {'strain': 0.001, 'dz': 0.0}
            elif variable_param == 'manual':
                constant_values = {'dz': 0.0}

        if variable_range is None and variable_param != 'manual':
            if variable_param == 'strain':
                variable_range = (-0.01, 0.01)
            elif variable_param == 'stress':
                variable_range = (0.0, 200.0)

        # Generate test inputs
        if variable_param == 'manual':
            if manual_inputs is None or 'strain' not in manual_inputs or 'stress' not in manual_inputs:
                raise ValueError("When variable_param='manual', you must provide manual_inputs with 'strain' and 'stress' arrays")

            strain_values = np.array(manual_inputs['strain'])
            stress_values = np.array(manual_inputs['stress'])

            if len(strain_values) != len(stress_values):
                raise ValueError("The strain and stress arrays must have the same length")

            num_points = len(strain_values)
            variable_values = np.arange(num_points)  # Just use indices for plotting

            logger.log(log_level, f"===== CONTROLLED INPUT TEST =====")
            logger.log(log_level, f"Variable parameter: {variable_param} (using manual inputs)")
            logger.log(log_level, f"Constant values: {constant_values}")
            logger.log(log_level, f"Number of test points: {num_points}")
            logger.log(log_level, f"Using {solver} solver for network predictions")
        else:
            min_val, max_val = variable_range
            variable_values = np.linspace(min_val, max_val, num_points)

            logger.log(log_level, f"===== CONTROLLED INPUT TEST =====")
            logger.log(log_level, f"Variable parameter: {variable_param}")
            logger.log(log_level, f"Constant values: {constant_values}")
            logger.log(log_level, f"Testing {variable_param} range: [{min_val}, {max_val}]")
            logger.log(log_level, f"Using {solver} solver for network predictions")

        # Initialize arrays for results
        pred_F = np.zeros((num_points, 1))
        pred_σ = np.zeros((num_points, 1))
        analytic_F = np.zeros((num_points, 1))
        analytic_σ = np.zeros((num_points, 1))

        # Store input values for reference
        strain_inputs = np.zeros(num_points)
        stress_inputs = np.zeros(num_points)

        E = 200000

        for i in range(num_points):
            if variable_param == 'strain':
                Dε = variable_values[i]
                σ_t = constant_values['stress']
                Dζ = constant_values['dz']
            elif variable_param == 'stress':
                Dε = constant_values['strain']
                σ_t = variable_values[i]
                Dζ = constant_values['dz']
            else:  # variable_param == 'manual'
                Dε = strain_values[i]
                σ_t = stress_values[i]
                Dζ = constant_values['dz']

            # Store input values for reference
            strain_inputs[i] = Dε
            stress_inputs[i] = σ_t

            F_analytic = 0.5 * (σ_t * (E ** -1) * σ_t) + 0.5 * E * ((Dε - Dζ) * (Dε - Dζ + (2 * σ_t) / E))
            σ_analytic = σ_t + E * (Dε - Dζ)

            # Store analytical values
            analytic_F[i, 0] = F_analytic
            analytic_σ[i, 0] = σ_analytic

            n_Dε = self._Normalize(np.array([[Dε]]), self.norm_params[0])
            n_σ_t = self._Normalize(np.array([[σ_t]]), self.norm_params[1])
            n_Dζ = self._Normalize(np.array([[Dζ]]), self.norm_params[2])
            input_i = tf.concat([n_Dε, n_σ_t, n_Dζ], axis=1)

            # Get predictions based on the selected solver
            if solver.lower() == 'network': n_F_pred = self.call(input_i)
            # elif solver.lower() == 'analytic': n_F_pred = self.get_analytic_F(input_i)
            else: raise ValueError(f"Unknown solver: {solver}. Use 'network' or 'analytic'.")

            F_pred_value = self._UnNormalize(n_F_pred, self.norm_params[3]).numpy()[0, 0]

            # Calculate stress using the selected solver
            n_σ_pred = self.get_stress(input_i, solver=solver)
            σ_pred_value = self._UnNormalize(n_σ_pred, self.norm_params[1]).numpy()[0, 0]

            # Store predictions
            pred_F[i, 0] = F_pred_value
            pred_σ[i, 0] = σ_pred_value

            # Log every 5th point
            if i % 5 == 0 or i == num_points - 1:
                logger.log(log_level, f"Point {i+1}/{num_points}: Dε={Dε:.6f}, σ_t={σ_t:.6f}")
                logger.log(log_level, f"  F: Predicted={F_pred_value:.6f}, Analytical={F_analytic:.6f}, Error={(F_pred_value-F_analytic)/F_analytic*100:.2f}%")
                logger.log(log_level, f"  σ: Predicted={σ_pred_value:.6f}, Analytical={σ_analytic:.6f}, Error={(σ_pred_value-σ_analytic)/σ_analytic*100:.2f}%")

        # Calculate errors
        F_errors_dict ={
            'absolute': (pred_F - analytic_F),
            'percent': (pred_F - analytic_F) / analytic_F * 100
        }
        σ_errors_dict ={
            'absolute': (pred_σ - analytic_σ),
            'percent': (pred_σ - analytic_σ) / analytic_σ * 100
        }
        #TODO: Determine which error type to use
        F_errors = F_errors_dict['absolute']
        σ_errors = σ_errors_dict['absolute']

        # Log summary statistics
        logger.log(log_level, "===== ERROR SUMMARY =====")
        logger.log(log_level, f"Average F error: {np.mean(np.abs(F_errors)):.2f}")
        logger.log(log_level, f"Average σ error: {np.mean(np.abs(σ_errors)):.2f}")
        if variable_param == 'strain':
            logger.log(log_level, f"Max F error: {np.max(np.abs(F_errors)):.2f} at Dε={variable_values[np.argmax(np.abs(F_errors))]}")
            logger.log(log_level, f"Max σ error: {np.max(np.abs(σ_errors)):.2f} at Dε={variable_values[np.argmax(np.abs(σ_errors))]}")
        else:  # variable_param == 'stress'
            logger.log(log_level, f"Max F error: {np.max(np.abs(F_errors)):.2f} at σ_t={variable_values[np.argmax(np.abs(F_errors))]}")
            logger.log(log_level, f"Max σ error: {np.max(np.abs(σ_errors)):.2f} at σ_t={variable_values[np.argmax(np.abs(σ_errors))]}")

        if plot_results:
            fig_path = self._plot_controlled_test_results(variable_values, variable_param, pred_F, pred_σ, analytic_F, analytic_σ, F_errors, σ_errors, solver)
        else: fig_path = None

        result_dict = {
            'variable_param': variable_param,
            'variable_values': variable_values.tolist(),
            'constant_values': constant_values,
            'inputs': {
                'strain': strain_inputs.tolist(),
                'stress': stress_inputs.tolist(),
                'dz': [constant_values['dz']] * num_points
            },
            'predictions': {
                'F': pred_F.flatten().tolist(),
                'σ': pred_σ.flatten().tolist()
            },
            'analytical': {
                'F': analytic_F.flatten().tolist(),
                'σ': analytic_σ.flatten().tolist()
            },
            'errors': {
                'F': F_errors.flatten().tolist(),
                'σ': σ_errors.flatten().tolist(),
                'avg_F_error': float(np.mean(np.abs(F_errors))),
                'avg_σ_error': float(np.mean(np.abs(σ_errors))),
                'max_F_error': float(np.max(np.abs(F_errors))),
                'max_σ_error': float(np.max(np.abs(σ_errors)))
            },
            'fig_path': fig_path
        }

        return result_dict

    def _plot_controlled_test_results(self, variable_values, variable_param, pred_F, pred_σ, analytic_F, analytic_σ, F_errors, σ_errors, solver):
        """Plot the results of the controlled input test.

        Args:
            variable_values: Array of variable parameter values (either Dε or σ_t)
            variable_param: Which parameter is variable ('strain' or 'stress')
            pred_F: Array of predicted F values
            pred_σ: Array of predicted σ values
            analytic_F: Array of analytical F values
            analytic_σ: Array of analytical σ values
            F_errors: Array of F errors (percent)
            σ_errors: Array of σ errors (percent)
            solver: Solver used for predictions ('network' or 'analytic')

        Returns:
            Path to the saved figure
        """
        plt.rcParams["font.family"] = "serif"
        plt.rc('axes.formatter', use_mathtext=True)
        plt.rcParams["font.serif"] = "cmr10"
        plt.rcParams['font.size']=12
        # Create directory if it doesn't exist
        save_dir = 'logs'
        os.makedirs(save_dir, exist_ok=True)

        # Use the same timestamp as the log file
        if not hasattr(self, '_debug_timestamp') or not self._debug_timestamp:
            self._debug_timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

        # Create figure with 4 subplots
        _, axs = plt.subplots(2, 2, figsize=(12, 10))

        if variable_param == 'strain':
            x_label = r'$\Delta\varepsilon$'
        elif variable_param == 'stress':
            x_label = r'$\sigma_t$'
        else:  # variable_param == 'manual'
            x_label = 'Test Point Index'

        # Plot F values
        axs[0, 0].plot(variable_values, pred_F, marker='o', linewidth=0, label=f'Predicted ({solver})')
        axs[0, 0].plot(variable_values, analytic_F, marker='x', linewidth=0, markersize=8, label='Analytical')
        axs[0, 0].set_xlabel(x_label)
        axs[0, 0].set_ylabel(r'$F_{t+1}$')
        axs[0, 0].legend(); axs[0, 0].ticklabel_format(style='sci', axis='both', scilimits=(0, 2))
        axs[0, 0].grid(True, linestyle='--', alpha=0.5)

        # Plot σ values
        axs[0, 1].plot(variable_values, pred_σ, marker='o', linewidth=0, label=f'Predicted ({solver})')
        axs[0, 1].plot(variable_values, analytic_σ,  marker='x', linewidth=0, label='Analytical')
        axs[0, 1].set_xlabel(x_label)
        axs[0, 1].set_ylabel(r'$\sigma_{t+1}$')
        axs[0, 1].legend(); axs[0, 1].ticklabel_format(style='sci', axis='both', scilimits=(0, 2))
        axs[0, 1].grid(True, linestyle='--', alpha=0.5)

        # Plot F errors
        axs[1, 0].plot(variable_values, F_errors, 'r-')
        axs[1, 0].set_title('Free Energy Error')
        axs[1, 0].set_xlabel(x_label)
        axs[1, 0].set_ylabel('Error'); axs[1, 0].ticklabel_format(style='sci', axis='both', scilimits=(0, 2))
        axs[1, 0].grid(True, linestyle='--', alpha=0.5)

        # Plot σ errors
        axs[1, 1].plot(variable_values, σ_errors, 'r-')
        axs[1, 1].set_title('Stress Error')
        axs[1, 1].set_xlabel(x_label)
        axs[1, 1].set_ylabel('Error'); axs[1, 1].ticklabel_format(style='sci', axis='both', scilimits=(0, 2))
        axs[1, 1].grid(True, linestyle='--', alpha=0.5)

        plt.tight_layout()

        # Save the figure
        fig_path = os.path.join(save_dir, f'controlled_test_{self._debug_timestamp}.png')
        plt.savefig(fig_path, dpi=300, bbox_inches='tight')

        logger = logging.getLogger('tann_debug')
        logger.info(f"Controlled test plot saved to {fig_path}")

        plt.show()

        return fig_path

    def test_network_with_variable_dz(self, constant_stress, constant_strain, dz_range, num_points=50, solver='network', log_level=logging.DEBUG, console_output=True, plot_results=True):
        """Test the network with variable internal variable increment (Dζ).

        This function tests the network with:
        - Constant stress (σ_t)
        - Constant strain increment (Dε)
        - Variable internal variable increment (Dζ)

        Args:
            constant_stress: The constant stress value to use for all test points
            constant_strain: The constant strain increment to use for all test points
            dz_range: Tuple of (min_dz, max_dz) for the internal variable increment range
            num_points: Number of test points to generate
            solver: Method to compute F_tdt, either 'network' or 'analytic'
            log_level: Logging level for this debug session
            console_output: Whether to output logs to console
            plot_results: Whether to plot the results

        Returns:
            Dictionary containing test inputs, predictions, analytical solutions, and errors
        """
        # Setup logger with specified console output preference
        self.setup_logger(log_level=log_level, console_output=console_output)
        logger = logging.getLogger('tann_debug')

        # Generate test inputs
        min_dz, max_dz = dz_range
        dz_values = np.linspace(min_dz, max_dz, num_points)

        # Initialize arrays for results
        pred_F = np.zeros((num_points, 1))
        pred_σ = np.zeros((num_points, 1))
        analytic_F = np.zeros((num_points, 1))
        analytic_σ = np.zeros((num_points, 1))

        # Store input values for reference
        strain_inputs = np.full(num_points, constant_strain)
        stress_inputs = np.full(num_points, constant_stress)

        # Calculate analytical solutions and network predictions
        logger.log(log_level, f"===== VARIABLE Dζ TEST =====")
        logger.log(log_level, f"Constant stress: {constant_stress}")
        logger.log(log_level, f"Constant strain: {constant_strain}")
        logger.log(log_level, f"Testing Dζ range: [{min_dz}, {max_dz}]")
        logger.log(log_level, f"Using {solver} solver for network predictions")

        E = 200000  # Young's modulus

        for i, dz_value in enumerate(dz_values):
            # Set input values
            Dε = constant_strain
            σ_t = constant_stress
            Dζ = dz_value

            F_analytic = 0.5 * (σ_t * (E ** -1) * σ_t) + \
                         0.5 * E * ((Dε - Dζ) * (Dε - Dζ + (2 * σ_t) / E))

            σ_analytic = σ_t + E * (Dε - Dζ)

            # Store analytical values
            analytic_F[i, 0] = F_analytic
            analytic_σ[i, 0] = σ_analytic

            # Normalize inputs for network
            n_Dε = self._Normalize(np.array([[Dε]]), self.norm_params[0])
            n_σ_t = self._Normalize(np.array([[σ_t]]), self.norm_params[1])
            n_Dζ = self._Normalize(np.array([[Dζ]]), self.norm_params[2])
            input_i = tf.concat([n_Dε, n_σ_t, n_Dζ], axis=1)

            # Get predictions based on the selected solver
            if solver.lower() == 'network':
                n_F_pred = self.call(input_i)
            elif solver.lower() == 'analytic':
                n_F_pred = self.get_analytic_F(input_i)
            else:
                raise ValueError(f"Unknown solver: {solver}. Use 'network' or 'analytic'.")

            F_pred_value = self._UnNormalize(n_F_pred, self.norm_params[3]).numpy()[0, 0]

            # Calculate stress using the selected solver
            n_σ_pred = self.get_stress(input_i, solver=solver)
            σ_pred_value = self._UnNormalize(n_σ_pred, self.norm_params[1]).numpy()[0, 0]

            # Store predictions
            pred_F[i, 0] = F_pred_value
            pred_σ[i, 0] = σ_pred_value

            # Log every 10th point
            if i % 5 == 0 or i == num_points - 1:
                logger.log(log_level, f"Point {i+1}/{num_points}: Dζ={Dζ:.6f}")
                logger.log(log_level, f"  F: Predicted={F_pred_value:.6f}, Analytical={F_analytic:.6f}, Error={(F_pred_value-F_analytic)/F_analytic*100:.2f}%")
                logger.log(log_level, f"  σ: Predicted={σ_pred_value:.6f}, Analytical={σ_analytic:.6f}, Error={(σ_pred_value-σ_analytic)/σ_analytic*100:.2f}%")

        # Calculate errors
        # F_errors = (pred_F - analytic_F) / analytic_F * 100  # percent error
        F_errors = (pred_F - analytic_F)
        # σ_errors = (pred_σ - analytic_σ) / analytic_σ * 100  # percent error
        σ_errors = (pred_σ - analytic_σ)

        # Log summary statistics
        logger.log(log_level, "===== ERROR SUMMARY =====")
        logger.log(log_level, f"Average F error: {np.mean(np.abs(F_errors)):.2f}")
        logger.log(log_level, f"Average σ error: {np.mean(np.abs(σ_errors)):.2f}")
        logger.log(log_level, f"Max F error: {np.max(np.abs(F_errors)):.2f} at Dζ={dz_values[np.argmax(np.abs(F_errors))]}")
        logger.log(log_level, f"Max σ error: {np.max(np.abs(σ_errors)):.2f} at Dζ={dz_values[np.argmax(np.abs(σ_errors))]}")

        # Plot results if requested
        if plot_results:
            # We'll use the existing plotting method with a custom variable parameter
            fig_path = self._plot_variable_dz_results(dz_values, pred_F, pred_σ, analytic_F, analytic_σ, F_errors, σ_errors, solver)
        else:
            fig_path = None

        # Prepare return dictionary
        result_dict = {
            'variable_param': 'dz',
            'variable_values': dz_values.tolist(),
            'constant_values': {
                'strain': constant_strain,
                'stress': constant_stress
            },
            'inputs': {
                'strain': strain_inputs.tolist(),
                'stress': stress_inputs.tolist(),
                'dz': dz_values.tolist()
            },
            'predictions': {
                'F': pred_F.flatten().tolist(),
                'σ': pred_σ.flatten().tolist()
            },
            'analytical': {
                'F': analytic_F.flatten().tolist(),
                'σ': analytic_σ.flatten().tolist()
            },
            'errors': {
                'F': F_errors.flatten().tolist(),
                'σ': σ_errors.flatten().tolist(),
                'avg_F_error': float(np.mean(np.abs(F_errors))),
                'avg_σ_error': float(np.mean(np.abs(σ_errors))),
                'max_F_error': float(np.max(np.abs(F_errors))),
                'max_σ_error': float(np.max(np.abs(σ_errors)))
            },
            'fig_path': fig_path
        }

        return result_dict

    def _plot_variable_dz_results(self, dz_values, pred_F, pred_σ, analytic_F, analytic_σ, F_errors, σ_errors, solver):
        """Plot the results of the variable Dζ test.

        Args:
            dz_values: Array of Dζ values
            pred_F: Array of predicted F values
            pred_σ: Array of predicted σ values
            analytic_F: Array of analytical F values
            analytic_σ: Array of analytical σ values
            F_errors: Array of F errors (percent)
            σ_errors: Array of σ errors (percent)
            solver: Solver used for predictions ('network' or 'analytic')

        Returns:
            Path to the saved figure
        """
        plt.rcParams["font.family"] = "serif"
        plt.rc('axes.formatter', use_mathtext=True)
        plt.rcParams["font.serif"] = "cmr10"
        plt.rcParams['font.size']=12
        # Create directory if it doesn't exist
        save_dir = 'logs'
        os.makedirs(save_dir, exist_ok=True)

        # Use the same timestamp as the log file
        if not hasattr(self, '_debug_timestamp') or not self._debug_timestamp:
            self._debug_timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

        # Create figure with 4 subplots
        _, axs = plt.subplots(2, 2, figsize=(12, 10))

        # Set x-axis label for Dζ
        x_label = r'$\Delta\zeta$'

        # Plot F values
        axs[0, 0].plot(dz_values, pred_F, marker='o', linewidth=0, label=f'Predicted ({solver})')
        axs[0, 0].plot(dz_values, analytic_F, marker='x', linewidth=0, markersize=8, label='Analytical')
        axs[0, 0].set_xlabel(x_label)
        axs[0, 0].set_ylabel(r'$F_{t+1}$')
        axs[0, 0].legend(); axs[0, 0].ticklabel_format(style='sci', axis='both', scilimits=(0, 2))
        axs[0, 0].grid(True, linestyle='--', alpha=0.5)

        # Plot σ values
        axs[0, 1].plot(dz_values, pred_σ, marker='o', linewidth=0, label=f'Predicted ({solver})')
        axs[0, 1].plot(dz_values, analytic_σ, marker='x', linewidth=0, label='Analytical')
        axs[0, 1].set_xlabel(x_label)
        axs[0, 1].set_ylabel(r'$\sigma_{t+1}$')
        axs[0, 1].legend(); axs[0, 1].ticklabel_format(style='sci', axis='both', scilimits=(0, 2))
        axs[0, 1].grid(True, linestyle='--', alpha=0.5)

        # Plot F errors
        axs[1, 0].plot(dz_values, F_errors, 'r-')
        axs[1, 0].set_title('Free Energy Error')
        axs[1, 0].set_xlabel(x_label)
        axs[1, 0].set_ylabel('Error'); axs[1, 0].ticklabel_format(style='sci', axis='both', scilimits=(0, 2))
        axs[1, 0].grid(True, linestyle='--', alpha=0.5)

        # Plot σ errors
        axs[1, 1].plot(dz_values, σ_errors, 'r-')
        axs[1, 1].set_title('Stress Error')
        axs[1, 1].set_xlabel(x_label)
        axs[1, 1].set_ylabel('Error'); axs[1, 1].ticklabel_format(style='sci', axis='both', scilimits=(0, 2))
        axs[1, 1].grid(True, linestyle='--', alpha=0.5)

        plt.tight_layout()

        # Save the figure
        fig_path = os.path.join(save_dir, f'variable_dz_test_{self._debug_timestamp}.png')
        plt.savefig(fig_path, dpi=300, bbox_inches='tight')

        logger = logging.getLogger('tann_debug')
        logger.info(f"Variable Dζ test plot saved to {fig_path}")

        plt.show()

        return fig_path