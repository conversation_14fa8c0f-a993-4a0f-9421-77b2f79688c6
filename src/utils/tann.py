"""
This module contains a wrapper as TANN model that connects the pretrained NNz and NNf models together.
This module will be used for iterative inference mode only.

Date: 2025-05-13
Author: <PERSON><PERSON><PERSON>
"""
#%% ---- Import Required Libraries ----
import warnings

import numpy as np
import tensorflow as tf
from classifier import DzBinaryClassifier
from keras.utils import custom_object_scope
from sub_nn import NNf, NNz


#%% ---- Model Class ----
class TANN (tf.keras.Model):
    """TANN model that connects the pretrained NNz and NNf models together."""
    def __init__ (self, NNz_model_dir:str, NNf_model_dir:str):
        """
        Initialize the TANN model by loading the pretrained NNz and NNf models.

        Parameters:
        - NNz_model_dir (str): Directory where the NNz model is saved.
        - NNf_model_dir (str): Directory where the NNf model is saved.
        """
        super().__init__()

        # Load NNz and NNf models
        with custom_object_scope({'_weighted_mae_loss': NNz._weighted_mae_loss}):
            self.NNz_model, self.NNz_metadata = NNz.load_model_(NNz_model_dir)
        self.NNf_model, self.NNf_metadata = NNf.load_model_(NNf_model_dir)

        # Extract normalization parameters
        self.NNz_norm_params = self.NNz_metadata['Normalizing Parameters']
        self.NNf_norm_params = self.NNf_metadata['Normalizing Parameters']

        assert self.NNz_norm_params[0:5] == self.NNf_norm_params, "Incosistent normalization parameters between NNz and NNf."

    def infer (self, input_test):
        # Extract inputs: Required for NNz or NNf
        Dε_full = tf.slice(input_test, [0, 0], [-1, 1])                         # strain increment
        σ_t_full = tf.slice(input_test, [0, 1], [-1, 1])                        # stress @ t
        # ε_t_full = tf.slice(input_test, [0, 5], [-1, 1])                        # total strain @ t
        acc_eq_pl_ε_t_full = tf.slice(input_test, [0, 7], [-1, 1])              # accumulated equivalent plastic strain @ t

        if σ_t_full[0, None] != 0: warnings.warn("The initial stress is not zero.")
        if acc_eq_pl_ε_t_full[0, None] != 0: warnings.warn("The initial accumulated equivalent plastic strain is not zero.")

        # Normalize inputs
        n_Dε_full = self._Normalize(Dε_full, self.NNz_norm_params[0])
        n_σ_t_full = self._Normalize(σ_t_full, self.NNz_norm_params[1])
        # n_ε_t_full = self._Normalize(ε_t_full, self.NNz_norm_params[5])
        n_acc_eq_pl_ε_t_full = self._Normalize(acc_eq_pl_ε_t_full, self.NNz_norm_params[7])

        # Necessary initial values to start the loop
        n_σ_t = n_σ_t_full[0, None]
        n_acc_eq_pl_ε_t = n_acc_eq_pl_ε_t_full[0, None]
        del n_σ_t_full, σ_t_full, n_acc_eq_pl_ε_t_full, acc_eq_pl_ε_t_full

        num_test = len(n_Dε_full)

        pred_D_eq_pl_ε, pred_F, pred_σ, D_eq_pl_ε_errors, F_errors, σ_errors, D_eq_pl_ε_errors_percent, F_errors_percent, σ_errors_percent = \
            np.empty((num_test, 1), dtype=np.float64), \
            np.empty((num_test, 1), dtype=np.float64), \
            np.empty((num_test, 1), dtype=np.float64), \
            np.empty((num_test, 1), dtype=np.float64), \
            np.empty((num_test, 1), dtype=np.float64), \
            np.empty((num_test, 1), dtype=np.float64), \
            np.empty((num_test, 1), dtype=np.float64), \
            np.empty((num_test, 1), dtype=np.float64), \
            np.empty((num_test, 1), dtype=np.float64)

        step_results = []

        for i in range(num_test):
            # NNz
            input_nnz_i = tf.concat([n_Dε_full[i, None], n_σ_t, n_acc_eq_pl_ε_t], axis=1, name='input_to_NNz')
            n_D_eq_pl_ε_pred = self.NNz_model.call(input_nnz_i)
            D_eq_pl_ε_pred = self._UnNormalize(n_D_eq_pl_ε_pred, self.NNz_norm_params[8])

            # convert NNz output to NNf input
            Dε = self._UnNormalize(n_Dε_full[i, None], self.NNz_norm_params[0])
            Dζ = tf.math.sign(Dε) * D_eq_pl_ε_pred
            n_Dζ = self._Normalize(Dζ, self.NNf_norm_params[2])

            # NNf
            input_nnf_i = tf.concat([n_Dε_full[i, None], n_σ_t, n_Dζ], axis=1, name='input_to_NNf')
            n_F_pred = self.NNf_model.call(input_nnf_i)
            n_σ_pred = self.NNf_model.get_stress(input_nnf_i, solver='network')

            F_pred = self._UnNormalize(n_F_pred, self.NNf_norm_params[3]).numpy()[0, 0]
            σ_pred = self._UnNormalize(n_σ_pred, self.NNf_norm_params[1]).numpy()[0, 0]

            pred_D_eq_pl_ε[i, 0] = D_eq_pl_ε_pred.numpy()[0, 0]
            pred_F[i, 0] = F_pred
            pred_σ[i, 0] = σ_pred

            # Calculate errors
            if input_test.shape[1] >= 9:
                true_D_eq_pl_ε = input_test[i, 8]
                true_F = input_test[i, 3]
                true_σ = input_test[i, 4]

                D_eq_pl_ε_error = D_eq_pl_ε_pred - true_D_eq_pl_ε
                F_error = F_pred - true_F
                σ_error = σ_pred - true_σ

                D_eq_pl_ε_error_percent = (D_eq_pl_ε_error / true_D_eq_pl_ε) * 100 if true_D_eq_pl_ε != 0 else float('inf')
                F_error_percent = (F_error / true_F) * 100 if true_F != 0 else float('inf')
                σ_error_percent = (σ_error / true_σ) * 100 if true_σ != 0 else float('inf')

                D_eq_pl_ε_errors[i, 0] = D_eq_pl_ε_error
                F_errors[i, 0] = F_error
                σ_errors[i, 0] = σ_error


                D_eq_pl_ε_errors_percent[i, 0] = D_eq_pl_ε_error_percent
                F_errors_percent[i, 0] = F_error_percent
                σ_errors_percent[i, 0] = σ_error_percent

            else:
                true_D_eq_pl_ε = None
                true_F = None
                true_σ = None
                D_eq_pl_ε_error = None
                F_error = None
                σ_error = None
                D_eq_pl_ε_error_percent = None
                F_error_percent = None
                σ_error_percent = None

            step_result = {
                'step': i,
                'inputs': {
                     'Dε': Dε,
                     'σ_t': self._UnNormalize(n_σ_t, self.NNz_norm_params[1]).numpy()[0, 0],
                    #  'ε_t': self._UnNormalize(n_ε_t_full[i, None], self.NNz_norm_params[5]).numpy()[0, 0],
                     'acc_eq_pl_ε_t': self._UnNormalize(n_acc_eq_pl_ε_t, self.NNz_norm_params[7]).numpy()[0, 0],
                },
                'predictions': {'D_eq_pl_ε': D_eq_pl_ε_pred, 'F': F_pred, 'σ': σ_pred}
            }

            # Add true values and errors if available
            if input_test.shape[1] >= 9:
                step_result['true_values'] = {'D_eq_pl_ε': true_D_eq_pl_ε, 'F': true_F, 'σ': true_σ}
                step_result['errors'] = {
                    'D_eq_pl_ε': D_eq_pl_ε_error,
                    'D_eq_pl_ε_percent': D_eq_pl_ε_error_percent,
                    'F': F_error,
                    'F_percent': F_error_percent,
                    'σ': σ_error,
                    'σ_percent': σ_error_percent
                }

            step_results.append(step_result)

            # Update stress and accumulated plastic strain for next step
            if i < num_test - 1:
                n_σ_t = n_σ_pred
                acc_eq_pl_ε_t = self._UnNormalize(n_acc_eq_pl_ε_t, self.NNz_norm_params[7])
                acc_eq_pl_ε_t = tf.add(acc_eq_pl_ε_t, D_eq_pl_ε_pred)
                n_acc_eq_pl_ε_t = self._Normalize(acc_eq_pl_ε_t, self.NNz_norm_params[7])


        summary = {
            'pred_D_eq_pl_ε': pred_D_eq_pl_ε.flatten().tolist(),
            'pred_F': pred_F.flatten().tolist(),
            'pred_σ': pred_σ.flatten().tolist()
        }

        # Add error statistics if target values were available
        if input_test.shape[1] >= 9:
            summary.update({
                'true_D_eq_pl_ε': input_test[:, 8].tolist(),
                'true_F': input_test[:, 3].tolist(),
                'true_σ': input_test[:, 4].tolist(),
                'D_eq_pl_ε_errors': D_eq_pl_ε_errors.flatten().tolist(),
                'F_errors': F_errors.flatten().tolist(),
                'σ_errors': σ_errors.flatten().tolist(),
                'D_eq_pl_ε_errors_percent': D_eq_pl_ε_errors_percent.flatten().tolist(),
                'F_errors_percent': F_errors_percent.flatten().tolist(),
                'σ_errors_percent': σ_errors_percent.flatten().tolist(),
                'avg_D_eq_pl_ε_error': float(np.mean(np.abs(D_eq_pl_ε_errors))),
                'avg_F_error': float(np.mean(np.abs(F_errors))),
                'avg_σ_error': float(np.mean(np.abs(σ_errors))),
                'avg_D_eq_pl_ε_error_percent': float(np.mean(np.abs(D_eq_pl_ε_errors_percent))),
                'avg_F_error_percent': float(np.mean(np.abs(F_errors_percent))),
                'avg_σ_error_percent': float(np.mean(np.abs(σ_errors_percent))),
                'max_D_eq_pl_ε_error': float(np.max(np.abs(D_eq_pl_ε_errors))),
                'max_F_error': float(np.max(np.abs(F_errors))),
                'max_σ_error': float(np.max(np.abs(σ_errors))),
                'max_D_eq_pl_ε_error_percent': float(np.max(np.abs(D_eq_pl_ε_errors_percent))),
                'max_F_error_percent': float(np.max(np.abs(F_errors_percent))),
                'max_σ_error_percent': float(np.max(np.abs(σ_errors_percent)))
            })


        result_dict = {
            'step_results': step_results,
            'summary': summary,
            'num_samples': num_test
        }

        return result_dict

    #%% Static Methods
    @staticmethod
    def _Normalize (u, param):
            '''Noramlize/Standardize the input (u)'''
            n_u = tf.divide(tf.math.add(u, -param[1]), param[0])
            return n_u

    @staticmethod
    def _UnNormalize (n_u, param):
            '''Un-noramlize/Un-standardize the input (n_u)'''
            u = tf.add(tf.multiply(n_u, param[0]), param[1])
            return u

#%% ---- TANN with Binary Classifier ----
class TANNWithClassifier(TANN):
    def __init__(self, NNz_model_dir:str, NNf_model_dir:str, classifier_dir=None, classifier_method='hybrid'):
        """
        Initialize TANN with a binary classifier for NNz.

        Parameters:
        - NNz_model_dir: Directory where the NNz model is saved
        - NNf_model_dir: Directory where the NNf model is saved
        - classifier_dir: Directory where the binary classifier is saved
        - classifier_method: Method to correct predictions of NNz ('hard', 'soft', 'adaptive', or 'hybrid')
        """
        super().__init__(NNz_model_dir, NNf_model_dir)

        # Store model directories for potential reuse
        self.NNz_model_dir = NNz_model_dir
        self.NNf_model_dir = NNf_model_dir

        # Load binary classifier if provided
        self.classifier = None
        self.classifier_method = classifier_method
        if classifier_dir:
            print('='*50)
            print(f"Loading binary classifier from {classifier_dir}")
            self.classifier = DzBinaryClassifier.load(classifier_dir)
            print(f"Using classifier method: {classifier_method}")
            print('='*50)

            # Check classifier input shape
            if hasattr(self.classifier, 'model') and hasattr(self.classifier.model, 'input_shape'):
                input_shape = self.classifier.model.input_shape
                if input_shape and len(input_shape) > 1:
                    expected_features = input_shape[1]
                    print(f"Classifier expects {expected_features} features")

    def infer(self, input_test):
        """
        Modified infer method that integrates the binary classifier directly.
        """
        # Extract inputs: Required for NNz or NNf
        Dε_full = tf.slice(input_test, [0, 0], [-1, 1])                         # strain increment
        σ_t_full = tf.slice(input_test, [0, 1], [-1, 1])                        # stress @ t
        acc_eq_pl_ε_t_full = tf.slice(input_test, [0, 7], [-1, 1])              # accumulated equivalent plastic strain @ t

        if σ_t_full[0, None] != 0: warnings.warn("The initial stress is not zero.")
        if acc_eq_pl_ε_t_full[0, None] != 0: warnings.warn("The initial accumulated equivalent plastic strain is not zero.")

        # Normalize inputs
        n_Dε_full = self._Normalize(Dε_full, self.NNz_norm_params[0])
        n_σ_t_full = self._Normalize(σ_t_full, self.NNz_norm_params[1])
        n_acc_eq_pl_ε_t_full = self._Normalize(acc_eq_pl_ε_t_full, self.NNz_norm_params[7])

        # Verify classifier input shape if available
        if self.classifier is not None and hasattr(self.classifier, 'model') and hasattr(self.classifier.model, 'input_shape'):
            input_shape = self.classifier.model.input_shape
            if input_shape and len(input_shape) > 1:
                expected_features = input_shape[1]
                if expected_features != 3:
                    raise ValueError(f"The current classifier used by the model expects {expected_features} features, but exactly 3 features are required. Please retrain the classifier with the correct input shape.")

        # Necessary initial values to start the loop
        n_σ_t = n_σ_t_full[0, None]
        n_acc_eq_pl_ε_t = n_acc_eq_pl_ε_t_full[0, None]
        del n_σ_t_full, σ_t_full, n_acc_eq_pl_ε_t_full, acc_eq_pl_ε_t_full

        num_test = len(n_Dε_full)

        pred_D_eq_pl_ε, pred_F, pred_σ = \
        np.empty((num_test, 1), dtype=np.float64), \
        np.empty((num_test, 1), dtype=np.float64), \
        np.empty((num_test, 1), dtype=np.float64)

        # Initialize error arrays if needed
        if input_test.shape[1] >= 9:
            D_eq_pl_ε_errors = np.empty((num_test, 1), dtype=np.float64)
            F_errors = np.empty((num_test, 1), dtype=np.float64)
            σ_errors = np.empty((num_test, 1), dtype=np.float64)
            D_eq_pl_ε_errors_percent = np.empty((num_test, 1), dtype=np.float64)
            F_errors_percent = np.empty((num_test, 1), dtype=np.float64)
            σ_errors_percent = np.empty((num_test, 1), dtype=np.float64)

        step_results = []

        for i in range(num_test):
            # NNz
            input_nnz_i = tf.concat([n_Dε_full[i, None], n_σ_t, n_acc_eq_pl_ε_t], axis=1, name='input_to_NNz')
            n_D_eq_pl_ε_pred = self.NNz_model.call(input_nnz_i)
            D_eq_pl_ε_pred_raw = self._UnNormalize(n_D_eq_pl_ε_pred, self.NNz_norm_params[8])

            # Apply binary classifier if available
            if self.classifier is not None:
                try:
                    # Prepare data for binary classifier (exactly 3 features)
                    classifier_input = np.array([[
                        self._UnNormalize(n_Dε_full[i, None], self.NNz_norm_params[0]).numpy()[0, 0],
                        self._UnNormalize(n_σ_t, self.NNz_norm_params[1]).numpy()[0, 0],
                        self._UnNormalize(n_acc_eq_pl_ε_t, self.NNz_norm_params[7]).numpy()[0, 0]
                    ]])

                    # Get binary prediction and probability
                    binary_pred, binary_prob = self.classifier.predict(classifier_input)

                    # Apply classifier decision based on selected method
                    if self.classifier_method == 'hard':
                        # Hard threshold (binary decision)
                        if binary_pred[0] == 0:  # If classifier predicts zero
                            D_eq_pl_ε_pred = tf.constant([[0.0]], dtype=tf.float64)
                        else:
                            D_eq_pl_ε_pred = D_eq_pl_ε_pred_raw

                    elif self.classifier_method == 'soft':
                        # Soft threshold (confidence-weighted)
                        # binary_prob[0][0] is the probability of being non-zero
                        D_eq_pl_ε_pred = D_eq_pl_ε_pred_raw * binary_prob[0][0]

                    elif self.classifier_method == 'adaptive':
                        # Adaptive threshold (based on prediction magnitude)
                        if binary_prob[0][0] < 0.3 or tf.abs(D_eq_pl_ε_pred_raw) < 1e-6:
                            D_eq_pl_ε_pred = tf.constant([[0.0]], dtype=tf.float64)
                        else:
                            D_eq_pl_ε_pred = D_eq_pl_ε_pred_raw

                    elif self.classifier_method == 'hybrid':
                        # Hybrid approach (combine confidence and magnitude)
                        zero_confidence = 1 - binary_prob[0][0]  # Confidence it's zero
                        magnitude_factor = tf.tanh(tf.abs(D_eq_pl_ε_pred_raw) * 1e5)  # Scale based on magnitude

                        if zero_confidence > 0.7 or (zero_confidence > 0.5 and magnitude_factor < 0.3):
                            D_eq_pl_ε_pred = tf.constant([[0.0]], dtype=tf.float64)
                        else:
                            D_eq_pl_ε_pred = D_eq_pl_ε_pred_raw

                    else:
                        D_eq_pl_ε_pred = D_eq_pl_ε_pred_raw

                except Exception as e:
                    print(f"Error applying classifier at step {i}: {e}")
                    D_eq_pl_ε_pred = D_eq_pl_ε_pred_raw  # Fall back to raw prediction
            else:
                D_eq_pl_ε_pred = D_eq_pl_ε_pred_raw

            # convert NNz output to NNf input
            Dε = self._UnNormalize(n_Dε_full[i, None], self.NNz_norm_params[0])
            Dζ = tf.math.sign(Dε) * D_eq_pl_ε_pred
            n_Dζ = self._Normalize(Dζ, self.NNf_norm_params[2])

            # NNf
            input_nnf_i = tf.concat([n_Dε_full[i, None], n_σ_t, n_Dζ], axis=1, name='input_to_NNf')
            n_F_pred = self.NNf_model.call(input_nnf_i)
            n_σ_pred = self.NNf_model.get_stress(input_nnf_i, solver='network')

            F_pred = self._UnNormalize(n_F_pred, self.NNf_norm_params[3]).numpy()[0, 0]
            σ_pred = self._UnNormalize(n_σ_pred, self.NNf_norm_params[1]).numpy()[0, 0]

            pred_D_eq_pl_ε[i, 0] = D_eq_pl_ε_pred.numpy()[0, 0]
            pred_F[i, 0] = F_pred
            pred_σ[i, 0] = σ_pred

            # Calculate errors if true values are available
            if input_test.shape[1] >= 9:
                true_D_eq_pl_ε = input_test[i, 8]
                true_F = input_test[i, 3]
                true_σ = input_test[i, 4]

                D_eq_pl_ε_error = D_eq_pl_ε_pred.numpy()[0, 0] - true_D_eq_pl_ε
                F_error = F_pred - true_F
                σ_error = σ_pred - true_σ

                D_eq_pl_ε_error_percent = (D_eq_pl_ε_error / true_D_eq_pl_ε) * 100 if true_D_eq_pl_ε != 0 else float('inf')
                F_error_percent = (F_error / true_F) * 100 if true_F != 0 else float('inf')
                σ_error_percent = (σ_error / true_σ) * 100 if true_σ != 0 else float('inf')

                D_eq_pl_ε_errors[i, 0] = D_eq_pl_ε_error
                F_errors[i, 0] = F_error
                σ_errors[i, 0] = σ_error

                D_eq_pl_ε_errors_percent[i, 0] = D_eq_pl_ε_error_percent
                F_errors_percent[i, 0] = F_error_percent
                σ_errors_percent[i, 0] = σ_error_percent

            # Store step result
            step_result = {
                'step': i,
                'inputs': {
                     'Dε': self._UnNormalize(n_Dε_full[i, None], self.NNz_norm_params[0]).numpy()[0, 0],
                     'σ_t': self._UnNormalize(n_σ_t, self.NNz_norm_params[1]).numpy()[0, 0],
                     'acc_eq_pl_ε_t': self._UnNormalize(n_acc_eq_pl_ε_t, self.NNz_norm_params[7]).numpy()[0, 0],
                },
                'predictions': {
                    'D_eq_pl_ε': D_eq_pl_ε_pred.numpy()[0, 0],
                    'F': F_pred,
                    'σ': σ_pred
                }
            }

            # Add classifier info if available
            if self.classifier is not None:
                try:
                    step_result['classifier'] = {
                        'binary_prediction': int(binary_pred[0]),
                        'confidence': float(binary_prob[0][0]),
                        'raw_prediction': float(D_eq_pl_ε_pred_raw.numpy()[0, 0]),
                        'final_prediction': float(D_eq_pl_ε_pred.numpy()[0, 0])
                    }
                except Exception as e:
                    step_result['classifier'] = {
                        'error': str(e)
                    }

            # Add true values and errors if available
            if input_test.shape[1] >= 9:
                step_result['true_values'] = {
                    'D_eq_pl_ε': true_D_eq_pl_ε,
                    'F': true_F,
                    'σ': true_σ
                }
                step_result['errors'] = {
                    'D_eq_pl_ε': D_eq_pl_ε_error,
                    'D_eq_pl_ε_percent': D_eq_pl_ε_error_percent,
                    'F': F_error,
                    'F_percent': F_error_percent,
                    'σ': σ_error,
                    'σ_percent': σ_error_percent
                }

            step_results.append(step_result)

            # Update stress and accumulated plastic strain for next step
            if i < num_test - 1:
                n_σ_t = n_σ_pred
                acc_eq_pl_ε_t = self._UnNormalize(n_acc_eq_pl_ε_t, self.NNz_norm_params[7])
                acc_eq_pl_ε_t = tf.add(acc_eq_pl_ε_t, D_eq_pl_ε_pred)
                n_acc_eq_pl_ε_t = self._Normalize(acc_eq_pl_ε_t, self.NNz_norm_params[7])

        # Prepare result dictionary
        summary = {
            'pred_D_eq_pl_ε': pred_D_eq_pl_ε.flatten().tolist(),
            'pred_F': pred_F.flatten().tolist(),
            'pred_σ': pred_σ.flatten().tolist()
        }

        # Add classifier info to summary if available
        if self.classifier is not None:
            summary['classifier_method'] = self.classifier_method

            # Count zero predictions
            zero_count = np.sum(np.abs(pred_D_eq_pl_ε) < 1e-10)
            summary['zero_predictions'] = int(zero_count)
            summary['zero_predictions_percent'] = float(zero_count / len(pred_D_eq_pl_ε) * 100)

        # Add error statistics if target values were available
        if input_test.shape[1] >= 9:
            summary.update({
                'true_D_eq_pl_ε': input_test[:, 8].tolist(),
                'true_F': input_test[:, 3].tolist(),
                'true_σ': input_test[:, 4].tolist(),
                'D_eq_pl_ε_errors': D_eq_pl_ε_errors.flatten().tolist(),
                'F_errors': F_errors.flatten().tolist(),
                'σ_errors': σ_errors.flatten().tolist(),
                'D_eq_pl_ε_errors_percent': D_eq_pl_ε_errors_percent.flatten().tolist(),
                'F_errors_percent': F_errors_percent.flatten().tolist(),
                'σ_errors_percent': σ_errors_percent.flatten().tolist(),
                'avg_D_eq_pl_ε_error': float(np.mean(np.abs(D_eq_pl_ε_errors))),
                'avg_F_error': float(np.mean(np.abs(F_errors))),
                'avg_σ_error': float(np.mean(np.abs(σ_errors))),
                'avg_D_eq_pl_ε_error_percent': float(np.mean(np.abs(D_eq_pl_ε_errors_percent))),
                'avg_F_error_percent': float(np.mean(np.abs(F_errors_percent))),
                'avg_σ_error_percent': float(np.mean(np.abs(σ_errors_percent))),
                'max_D_eq_pl_ε_error': float(np.max(np.abs(D_eq_pl_ε_errors))),
                'max_F_error': float(np.max(np.abs(F_errors))),
                'max_σ_error': float(np.max(np.abs(σ_errors))),
                'max_D_eq_pl_ε_error_percent': float(np.max(np.abs(D_eq_pl_ε_errors_percent))),
                'max_F_error_percent': float(np.max(np.abs(F_errors_percent))),
                'max_σ_error_percent': float(np.max(np.abs(σ_errors_percent)))
            })

        result_dict = {
            'step_results': step_results,
            'summary': summary,
            'num_samples': num_test
        }

        return result_dict

    def compare_with_without_classifier(self, test_data, visualize:bool=True, plots_dir:str='../../results/classifier_comparison'):
        """
        Compare predictions with and without the binary classifier.

        Parameters:
        - test_data: Test data to use for comparison
        - visualize: Whether to visualize the comparison results
        - plots_dir: Directory to save the plots

        Returns:
        - Dictionary with comparison results
        """
        # Create a temporary TANN without classifier
        temp_tann = TANN(self.NNz_model_dir, self.NNf_model_dir)

        with_classifier = self.infer(test_data)
        without_classifier = temp_tann.infer(test_data)

        pred_with = np.array(with_classifier['summary']['pred_D_eq_pl_ε'])
        pred_without = np.array(without_classifier['summary']['pred_D_eq_pl_ε'])

        # Compare
        diff = np.abs(pred_with - pred_without)

        # Count significant differences
        significant_diff_threshold = 1e-6
        significant_diffs = np.where(diff > significant_diff_threshold)[0]

        comparison = {
            'max_diff': float(np.max(diff)),
            'mean_diff': float(np.mean(diff)),
            'num_different': int(np.sum(diff > 1e-10)),
            'total_samples': len(diff),
            'significant_diffs': len(significant_diffs),
            'significant_diff_indices': significant_diffs.tolist()[:10],  # Show first 10 indices
        }

        if test_data.shape[1] >= 9:
            true_values = test_data[:, 8]

            # Calculate errors
            errors_with = np.abs(pred_with.flatten() - true_values)
            errors_without = np.abs(pred_without.flatten() - true_values)

            # Calculate MAE
            mae_with = np.mean(errors_with)
            mae_without = np.mean(errors_without)

            # Add to comparison
            comparison.update({
                'mae_with_classifier': float(mae_with),
                'mae_without_classifier': float(mae_without),
                'mae_improvement': float((mae_without - mae_with) / mae_without * 100) if mae_without > 0 else 0.0,
                'zero_targets': int(np.sum(true_values == 0)),
                'zero_targets_percent': float(np.sum(true_values == 0) / len(true_values) * 100)
            })

            # Calculate errors specifically for zero targets
            zero_indices = np.where(true_values == 0)[0]
            if len(zero_indices) > 0:
                zero_errors_with = errors_with[zero_indices]
                zero_errors_without = errors_without[zero_indices]

                comparison.update({
                    'zero_targets_mae_with': float(np.mean(zero_errors_with)),
                    'zero_targets_mae_without': float(np.mean(zero_errors_without)),
                    'zero_targets_mae_improvement': float((np.mean(zero_errors_without) - np.mean(zero_errors_with)) / np.mean(zero_errors_without) * 100) if np.mean(zero_errors_without) > 0 else 0.0
                })

        if visualize:
            self.visualize_comparison_results(comparison_dict=comparison,
                                              test_data=test_data,
                                              regular_pred=pred_without,
                                              classifier_pred=pred_with,
                                              plots_dir=plots_dir)

        return comparison

    def visualize_comparison_results(self, comparison_dict, test_data, regular_pred=None, classifier_pred=None, plots_dir='../../results/classifier_comparison'):
        """
        Visualize the comparison results between predictions with and without the classifier.

        Parameters:
        - comparison (dict): Comparison dictionary returned by compare_with_without_classifier
        - test_data (array): Test data used for predictions
        - regular_pred (array, optional): Predictions without classifier. If None, will be extracted from comparison
        - classifier_pred (array, optional): Predictions with classifier. If None, will be extracted from comparison
        - plots_dir (str): Directory to save the plots
        """
        import os

        import matplotlib.pyplot as plt
        import numpy as np

        os.makedirs(plots_dir, exist_ok=True)

        # Extract predictions if not provided
        if regular_pred is None and 'pred_without' in comparison_dict:
            regular_pred = np.array(comparison_dict['pred_without'])

        if classifier_pred is None and 'pred_with' in comparison_dict:
            classifier_pred = np.array(comparison_dict['pred_with'])

        # If predictions are still None, run inference to get them
        if regular_pred is None or classifier_pred is None:
            temp_tann = TANN(self.NNz_model_dir, self.NNf_model_dir)

            with_classifier_results = self.infer(test_data)
            without_classifier_results = temp_tann.infer(test_data)

            regular_pred = np.array(without_classifier_results['summary']['pred_D_acc_eq_pl_ε'])
            classifier_pred = np.array(with_classifier_results['summary']['pred_D_acc_eq_pl_ε'])

        # Ensure predictions are flattened
        if len(regular_pred.shape) > 1:
            regular_pred = regular_pred.flatten()
        if len(classifier_pred.shape) > 1:
            classifier_pred = classifier_pred.flatten()

        # Get strain values for x-axis
        strain = np.cumsum(test_data[:, 0])

        # Calculate differences
        diff = np.abs(regular_pred - classifier_pred)

        # 1. Plot original vs. classifier-enhanced predictions
        plt.figure(figsize=(12, 6))
        plt.plot(strain, regular_pred, 'b-', label='Without Classifier', linewidth=2, alpha=0.7)
        plt.plot(strain, classifier_pred, 'r-', label='With Classifier', linewidth=2, alpha=0.7)

        # If true values are available, add them to the plot
        if test_data.shape[1] >= 9:
            true_values = test_data[:, 8]
            plt.plot(strain, true_values, 'g--', label='True Values', linewidth=2)

        plt.title('Comparison of Predictions With and Without Classifier')
        plt.xlabel('Strain')
        plt.ylabel('Δζ (Plastic Strain Increment)')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig(os.path.join(plots_dir, 'predictions_comparison.png'), dpi=300)
        plt.close()

        # 2. Plot the differences between predictions
        plt.figure(figsize=(12, 6))
        plt.plot(strain, diff, 'r-', linewidth=2)
        plt.title('Absolute Difference Between Predictions')
        plt.xlabel('Strain')
        plt.ylabel('|Δζ_without - Δζ_with|')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig(os.path.join(plots_dir, 'prediction_differences.png'), dpi=300)
        plt.close()

        # 3. If true values are available, create a bar chart comparing MAE
        if 'mae_with_classifier' in comparison_dict:
            plt.figure(figsize=(10, 6))

            # Overall MAE comparison
            mae_values = [comparison_dict['mae_without_classifier'], comparison_dict['mae_with_classifier']]
            labels = ['Without Classifier', 'With Classifier']
            colors = ['blue', 'red']

            plt.bar(labels, mae_values, color=colors, alpha=0.7)
            plt.title('Mean Absolute Error Comparison')
            plt.ylabel('MAE')
            plt.grid(True, linestyle='--', alpha=0.3, axis='y')

            # Add improvement percentage as text
            improvement = comparison_dict['mae_improvement']
            if improvement > 0:
                plt.text(1, mae_values[1] * 1.05, f"{improvement:.2f}% improvement",
                        ha='center', va='bottom', color='green', fontweight='bold')

            plt.tight_layout()
            plt.savefig(os.path.join(plots_dir, 'mae_comparison.png'), dpi=300)
            plt.close()

            # If zero targets metrics are available, create a separate bar chart
            if 'zero_targets_mae_with' in comparison_dict:
                plt.figure(figsize=(10, 6))

                # Zero targets MAE comparison
                zero_mae_values = [comparison_dict['zero_targets_mae_without'], comparison_dict['zero_targets_mae_with']]

                plt.bar(labels, zero_mae_values, color=colors, alpha=0.7)
                plt.title('Mean Absolute Error Comparison (Zero Targets Only)')
                plt.ylabel('MAE')
                plt.grid(True, linestyle='--', alpha=0.3, axis='y')

                # Add improvement percentage as text
                zero_improvement = comparison_dict['zero_targets_mae_improvement']
                if zero_improvement > 0:
                    plt.text(1, zero_mae_values[1] * 1.05, f"{zero_improvement:.2f}% improvement",
                            ha='center', va='bottom', color='green', fontweight='bold')

                plt.tight_layout()
                plt.savefig(os.path.join(plots_dir, 'zero_targets_mae_comparison.png'), dpi=300)
                plt.close()

        # 4. Create a scatter plot highlighting significant differences
        plt.figure(figsize=(12, 8))

        # Plot all points
        plt.scatter(regular_pred, classifier_pred,
                    c='blue', alpha=0.5, label='All predictions')

        # Highlight points with significant differences
        significant_diff_threshold = 1e-6
        significant_indices = np.where(diff > significant_diff_threshold)[0]

        if len(significant_indices) > 0:
            plt.scatter(regular_pred[significant_indices],
                        classifier_pred[significant_indices],
                        c='red', s=80, alpha=0.7, label=f'Significant differences (>{significant_diff_threshold})')

        # Add diagonal line (perfect agreement)
        min_val = min(np.min(regular_pred), np.min(classifier_pred))
        max_val = max(np.max(regular_pred), np.max(classifier_pred))
        plt.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.7, label='Perfect agreement')

        plt.title('Scatter Plot of Predictions: With vs. Without Classifier')
        plt.xlabel('Predictions Without Classifier')
        plt.ylabel('Predictions With Classifier')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.axis('equal')
        plt.tight_layout()
        plt.savefig(os.path.join(plots_dir, 'predictions_scatter.png'), dpi=300)
        plt.close()

        # 5. If true values are available, create a plot showing errors
        if test_data.shape[1] >= 9:
            true_values = test_data[:, 8]

            # Calculate errors
            errors_without = np.abs(regular_pred - true_values)
            errors_with = np.abs(classifier_pred - true_values)

            # Plot errors
            plt.figure(figsize=(12, 6))
            plt.plot(strain, errors_without, 'b-', label='Error Without Classifier', linewidth=2, alpha=0.7)
            plt.plot(strain, errors_with, 'r-', label='Error With Classifier', linewidth=2, alpha=0.7)

            plt.title('Absolute Errors Comparison')
            plt.xlabel('Strain')
            plt.ylabel('|Δζ_pred - Δζ_true|')
            plt.legend()
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.tight_layout()
            plt.savefig(os.path.join(plots_dir, 'error_comparison.png'), dpi=300)
            plt.close()

            # Plot error improvement
            error_improvement = errors_without - errors_with
            plt.figure(figsize=(12, 6))
            plt.plot(strain, error_improvement, 'g-', linewidth=2)
            plt.axhline(y=0, color='k', linestyle='--', alpha=0.7)

            plt.title('Error Improvement with Classifier')
            plt.xlabel('Strain')
            plt.ylabel('Error Improvement (Positive = Better)')
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.tight_layout()
            plt.savefig(os.path.join(plots_dir, 'error_improvement.png'), dpi=300)
            plt.close()

            # Create a histogram of error improvements
            plt.figure(figsize=(10, 6))
            plt.hist(error_improvement, bins=30, alpha=0.7, color='green')
            plt.axvline(x=0, color='k', linestyle='--', alpha=0.7)

            plt.title('Distribution of Error Improvements')
            plt.xlabel('Error Improvement (Positive = Better)')
            plt.ylabel('Frequency')
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.tight_layout()
            plt.savefig(os.path.join(plots_dir, 'error_improvement_histogram.png'), dpi=300)
            plt.close()

        # 6. Create a summary plot with multiple subplots
        plt.figure(figsize=(15, 10))

        # Subplot 1: Predictions comparison
        plt.subplot(2, 2, 1)
        plt.plot(strain, regular_pred, 'b-', label='Without', linewidth=1.5, alpha=0.7)
        plt.plot(strain, classifier_pred, 'r-', label='With', linewidth=1.5, alpha=0.7)
        if test_data.shape[1] >= 9:
            plt.plot(strain, true_values, 'g--', label='True', linewidth=1.5)
        plt.title('Predictions Comparison')
        plt.xlabel('Strain')
        plt.ylabel('Δζ')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)

        # Subplot 2: Differences
        plt.subplot(2, 2, 2)
        plt.plot(strain, diff, 'r-', linewidth=1.5)
        plt.title('Absolute Differences')
        plt.xlabel('Strain')
        plt.ylabel('|Δζ_without - Δζ_with|')
        plt.grid(True, linestyle='--', alpha=0.7)

        # Subplot 3: Scatter plot
        plt.subplot(2, 2, 3)
        plt.scatter(regular_pred, classifier_pred, c='blue', alpha=0.5, s=20)
        if len(significant_indices) > 0:
            plt.scatter(regular_pred[significant_indices],
                        classifier_pred[significant_indices],
                        c='red', s=40, alpha=0.7)
        plt.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.7)
        plt.title('Predictions Scatter Plot')
        plt.xlabel('Without Classifier')
        plt.ylabel('With Classifier')
        plt.grid(True, linestyle='--', alpha=0.7)

        # Subplot 4: Error comparison (if available)
        plt.subplot(2, 2, 4)
        if test_data.shape[1] >= 9:
            plt.plot(strain, errors_without, 'b-', label='Without', linewidth=1.5, alpha=0.7)
            plt.plot(strain, errors_with, 'r-', label='With', linewidth=1.5, alpha=0.7)
            plt.title('Error Comparison')
            plt.xlabel('Strain')
            plt.ylabel('Absolute Error')
            plt.legend()
            plt.grid(True, linestyle='--', alpha=0.7)
        else:
            plt.text(0.5, 0.5, 'No true values available for error comparison',
                    ha='center', va='center', fontsize=12)
            plt.axis('off')

        plt.tight_layout()
        plt.savefig(os.path.join(plots_dir, 'summary_comparison.png'), dpi=300)
        plt.close()

        print(f"\nAll plots saved to: {plots_dir}")

        return plots_dir