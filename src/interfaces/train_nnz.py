#%% ---- Import Required Libraries ----
import random
import sys

import numpy as np
import tensorflow as tf

sys.path.append('/home/<USER>/phd/FallahnejadA/phd_gitlab/tasks/TANN_v4/src/utils')

from funcs import get_data, pre_process_data, process_data, save_train_history, get_ROS_data
from plots import Plotter
from sub_nn import NNz

#%% ---- Set Random Seed ----
seed = 42
random.seed(seed)
np.random.seed(seed)
tf.random.set_seed(seed)

#%% ---- Load Data ----
E=200000
dataset_name = '10'
train_data=pre_process_data(get_data(f'{dataset_name}.1',E))
val_data=pre_process_data(get_data(f'{dataset_name}.2',E))

# train_data, val_data, test_data = split_dataset('10.00', E, train_size=1500, val_size=750, test_size=750,
#                               random_state=42, plot_distributions=False)

# train_data, val_data, test_data = get_ROS_data(folder_name='10.00', E=E, train_size=1500, val_size=750, ratio=0.8)

n_train_data, n_val_data, norm_params = process_data(train_data, val_data)

# %% Model Configuration
activation_dict = {
    'custom_act': 'custom_act',
    'relu': tf.keras.activations.relu,
    'leaky_relu': lambda: tf.keras.layers.LeakyReLU(alpha=0.2),
    'sigmoid': tf.keras.activations.sigmoid,
    'tanh': tf.keras.activations.tanh,
    'elu': tf.keras.activations.elu,
}

# TODO: Determin the hyperparameters:
            # 1- Number of hidden layers and their dimensions. For example: [48] means one hidden layer with 48 nodes.
            # 2- Initial Learning rate, number of epochs, and batch size.
            # 3- Activation function.
            # 4- Learning rate schedule type: 'exponential', 'cosine', or 'constant'
            # 5- Loss function: 'weighted_mae' or 'physics_informed'

hidden_dims = [48, 48]
learningRate=0.0003; nEpochs = 3500; bSize = 32  # Reduced LR for stability
activation_function = 'leaky_relu'
lr_schedule_type = 'cosine'

# OPTION 1: Corrected Exponential (Reduced Scale)
loss_config_exponential = {
    'base_weight': 1.0,
    'non_zero_weight': 8.0,
    'lambda_penalty': 0.1,           # Much lower for exponential
    'zero_penalty_weight': 0.1,      # Much lower to prevent overflow
    'use_physical_space': True,
    'penalty_type': 'exponential',
    'exponential_scale': 1e5,        # Reduced from 1e8 to prevent overflow
    'penalty_scale_factor': 1.0
}

# OPTION 2: Safer Scaled Quadratic (Recommended)
loss_config_scaled = {
    'base_weight': 1.0,
    'non_zero_weight': 8.0,
    'lambda_penalty': 8.2,          # Higher weight for scaled quadratic
    'zero_penalty_weight': 1.0,
    'use_physical_space': True,
    'penalty_type': 'scaled_quadratic',  # Auto-scales, more stable
    'penalty_scale_factor': 1.0
}

# OPTION 3: Extreme Linear (Fallback)
loss_config_linear = {
    'base_weight': 1.0,
    'non_zero_weight': 8.0,
    'lambda_penalty': 100.0,         # High but manageable
    'zero_penalty_weight': 10.0,
    'use_physical_space': True,
    'penalty_type': 'linear',
    'penalty_scale_factor': 1e4      # Moderate scaling
}

# Choose configuration (start with scaled_quadratic)
loss_config = loss_config_scaled

model_NNz = NNz(norm_params=norm_params, hidden_dims=hidden_dims, activation_func=activation_function)
print(model_NNz.summary())
history = NNz.train_model(model_NNz,
                          train_data=n_train_data,
                          val_data=n_val_data,
                          LearningRate=learningRate, nEpochs=nEpochs, bSize=bSize, silent_training=False,
                          lr_schedule_type=lr_schedule_type, loss_config=loss_config)

# %% Save Model
# model_path = NNz.save_model(model_NNz, dataset_name=f'{dataset_name}',
#                             learning_rate=learningRate, LR_schedule_type=lr_schedule_type, num_epochs=nEpochs, batch_size=bSize,
#                             norm_params=norm_params, save_dir='../../saved_models/NNz')

# save_train_history(history, model_path, filename='training_history')

#%% Plot
plotter = Plotter()
plotter.plot_loss(history=history, save_path=None)


#%% TEMP test
test_data = pre_process_data(get_data('10.3', E))
results = model_NNz.infer(test_data)
pred_Dζ = np.array(results['summary']['pred_Dζ'])

plotter.plot_nnz_prediction(y_pred=pred_Dζ, test_data=test_data, xaxis='strain')
plotter.plot_nnz_prediction(y_pred=pred_Dζ, test_data=test_data, xaxis='increment')
