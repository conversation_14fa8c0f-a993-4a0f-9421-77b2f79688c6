"""
This script provides a comprehensive analysis of the training history of a TANN model.

Date: 2025-05-18
Author: <PERSON><PERSON><PERSON>
"""

#%% ---- Import Required Libraries ----
import sys
import os
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# Add utils directory to path
sys.path.append('/home/<USER>/phd/FallahnejadA/phd_gitlab/tasks/TANN_v4/src/utils')

from funcs import load_training_history

#%% ---- Configuration ----
# Specify the model directory containing the saved history
# Replace this with your actual model directory
model_dir = "../../saved_models/NNz/NNz_dataset10_lr0.0003_cosine_5000epochs_32batch_physics_informed"

# Check if the directory exists
if not os.path.exists(model_dir):
    print(f"Model directory {model_dir} not found.")
    print("Please update the model_dir variable with the correct path to your saved model.")
    # List available model directories
    base_dir = "../../saved_models/NNz"
    if os.path.exists(base_dir):
        print("\nAvailable NNz model directories:")
        for d in os.listdir(base_dir):
            if os.path.isdir(os.path.join(base_dir, d)):
                print(f"- {d}")
    sys.exit(1)

# Path to the saved history file
history_file = os.path.join(model_dir, "training_history.pkl")

if not os.path.exists(history_file):
    print(f"History file {history_file} not found.")
    print("Make sure you've saved the training history using save_training_history().")
    sys.exit(1)

#%% ---- Load Training History ----
print(f"Loading training history from {history_file}...")
history = load_training_history(history_file)

# Print available metrics
print("\nAvailable metrics in the history:")
for key in history.keys():
    print(f"- {key}")

#%% ---- Basic Analysis ----
# Create epochs array
epochs = range(1, len(history['loss']) + 1)

# Calculate best epoch and values
best_epoch = np.argmin(history['val_loss']) + 1
best_val_loss = history['val_loss'][best_epoch - 1]
best_train_loss = history['loss'][best_epoch - 1]

print(f"\nTraining Summary:")
print(f"Total epochs: {len(epochs)}")
print(f"Best epoch: {best_epoch}")
print(f"Best validation loss: {best_val_loss:.6f}")
print(f"Training loss at best epoch: {best_train_loss:.6f}")

#%% ---- Plot Training and Validation Loss ----
plt.figure(figsize=(12, 8))

# Main loss plot
plt.subplot(2, 2, 1)
plt.plot(epochs, history['loss'], 'b-', label='Training')
plt.plot(epochs, history['val_loss'], 'r-', label='Validation')
plt.axvline(x=best_epoch, color='g', linestyle='--', label=f'Best epoch ({best_epoch})')
plt.title('Total Loss')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.yscale('log')
plt.legend()
plt.grid(True, linestyle='--', alpha=0.7)

# Plot loss components if available
if '_base_loss_metric' in history:
    plt.subplot(2, 2, 2)
    plt.plot(epochs, history['_base_loss_metric'], 'b-', label='Training')
    plt.plot(epochs, history['val__base_loss_metric'], 'r-', label='Validation')
    plt.title('Base Loss (Weighted MAE)')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.yscale('log')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)

if '_negative_penalty_metric' in history:
    plt.subplot(2, 2, 3)
    plt.plot(epochs, history['_negative_penalty_metric'], 'b-', label='Training')
    plt.plot(epochs, history['val__negative_penalty_metric'], 'r-', label='Validation')
    plt.title('Negative Penalty')
    plt.xlabel('Epoch')
    plt.ylabel('Penalty')
    plt.yscale('log')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # Calculate percentage of total loss from negative penalty
    plt.subplot(2, 2, 4)
    penalty_percentage = [100 * p / t if t > 0 else 0 for p, t in zip(history['_negative_penalty_metric'], history['loss'])]
    val_penalty_percentage = [100 * p / t if t > 0 else 0 for p, t in zip(history['val__negative_penalty_metric'], history['val_loss'])]
    plt.plot(epochs, penalty_percentage, 'b-', label='Training')
    plt.plot(epochs, val_penalty_percentage, 'r-', label='Validation')
    plt.title('Negative Penalty (% of Total Loss)')
    plt.xlabel('Epoch')
    plt.ylabel('Percentage')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)

plt.tight_layout()
plt.savefig(os.path.join(model_dir, 'history_analysis.png'), dpi=300, bbox_inches='tight')
plt.show()

#%% ---- Learning Rate Analysis (if available) ----
if 'lr' in history:
    plt.figure(figsize=(10, 6))
    plt.plot(epochs, history['lr'], 'g-')
    plt.title('Learning Rate Schedule')
    plt.xlabel('Epoch')
    plt.ylabel('Learning Rate')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.savefig(os.path.join(model_dir, 'learning_rate_schedule.png'), dpi=300, bbox_inches='tight')
    plt.show()

#%% ---- Loss vs Learning Rate Analysis (if available) ----
if 'lr' in history:
    plt.figure(figsize=(10, 6))
    plt.semilogx(history['lr'], history['loss'], 'b-', label='Training Loss')
    plt.semilogx(history['lr'], history['val_loss'], 'r-', label='Validation Loss')
    plt.title('Loss vs Learning Rate')
    plt.xlabel('Learning Rate (log scale)')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.savefig(os.path.join(model_dir, 'loss_vs_lr.png'), dpi=300, bbox_inches='tight')
    plt.show()

#%% ---- Advanced Analysis: Moving Averages ----
# Calculate moving averages to smooth the curves
window_size = 20
if len(epochs) > window_size:
    loss_ma = np.convolve(history['loss'], np.ones(window_size)/window_size, mode='valid')
    val_loss_ma = np.convolve(history['val_loss'], np.ones(window_size)/window_size, mode='valid')
    epochs_ma = epochs[window_size-1:]
    
    plt.figure(figsize=(10, 6))
    plt.plot(epochs, history['loss'], 'b-', alpha=0.3, label='Training Loss')
    plt.plot(epochs, history['val_loss'], 'r-', alpha=0.3, label='Validation Loss')
    plt.plot(epochs_ma, loss_ma, 'b-', linewidth=2, label=f'Training Loss ({window_size}-epoch MA)')
    plt.plot(epochs_ma, val_loss_ma, 'r-', linewidth=2, label=f'Validation Loss ({window_size}-epoch MA)')
    plt.title(f'Loss with {window_size}-Epoch Moving Average')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.yscale('log')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.savefig(os.path.join(model_dir, 'loss_moving_average.png'), dpi=300, bbox_inches='tight')
    plt.show()

print("\nAnalysis complete. Plots saved to the model directory.")
