"""
This script serves as the test interface for the TANN-f model.

Date: 2025-03-10
Author: <PERSON><PERSON><PERSON>
"""
#%% ---- Import Required Libraries ----
import sys

import numpy as np
import tensorflow as tf

sys.path.append('/home/<USER>/phd/FallahnejadA/phd_gitlab/tasks/TANN_v4/src/utils')

from funcs import apply_normalization, get_data, pre_process_data
from plots import Plotter
from sub_nn import NNf

#%% ---- Load Model ----
tf.keras.backend.set_floatx('float64')
#TODO: Specify the model directory
model_dir = '../../saved_models/NNf/D10_expoDecayLR_LR0.0003_BS20_E3500_HL1_N(48_1)_ACTcustom_act_2025-04-15_10-42-57'
loaded_net, metadata = NNf.load_model_(model_dir)

#%% ---- Inference ----
E = 200000
test_data = pre_process_data(get_data('15.22', E))           #TODO: specify the test dataset

#%% ---- Run Inference ----
results = loaded_net.infer(test_data[:, 0:3], solver='network')
F_tdt = np.array(results['summary']['pred_F'])
σ_tdt = np.array(results['summary']['pred_σ'])

#%% ---- Plot Predictions ----
plotter = Plotter()
plotter.plot_prediction(y_pred=F_tdt, test_data=test_data, title='F_tdt', xaxis='strain')
plotter.plot_prediction(y_pred=σ_tdt, test_data=test_data, title='σ_tdt', xaxis='strain')

#%% ---- Print summary statistics ----
# print(f"\n===== Inference Results Summary ({results['solver']} solver) =====")
# print(f"Number of samples: {results['num_samples']}")
# print(f"Average F Error: {results['summary']['avg_F_error_percent']:.4f}%")
# print(f"Average σ Error: {results['summary']['avg_σ_error_percent']:.4f}%")
# print(f"Max F Error: {results['summary']['max_F_error_percent']:.4f}%")
# print(f"Max σ Error: {results['summary']['max_σ_error_percent']:.4f}%")
#%% ---- Individual Step Results ----
# print("\n===== Example Step Results =====")
# step_index = 5  # Show results for step 5
# step_result = results['step_results'][step_index]
# print(f"Step {step_index} Results:")
# print(f"Inputs: Dε={step_result['inputs']['Dε']:.6f}, σ_t={step_result['inputs']['σ_t']:.6f}, Dζ={step_result['inputs']['Dζ']:.6f}")
# print(f"Predictions: F={step_result['predictions']['F']:.6f}, σ={step_result['predictions']['σ']:.6f}")
# print(f"True Values: F={step_result['true_values']['F']:.6f}, σ={step_result['true_values']['σ']:.6f}")
# print(f"Errors: F={step_result['errors']['F_percent']:.4f}%, σ={step_result['errors']['σ_percent']:.4f}%")