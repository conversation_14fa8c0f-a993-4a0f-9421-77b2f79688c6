#%% ---- Import Required Libraries ----
import random
import sys
import numpy as np
import tensorflow as tf
import matplotlib.pyplot as plt

sys.path.append('/home/<USER>/phd/FallahnejadA/phd_gitlab/tasks/TANN_v4/src/utils')

from funcs import get_data, pre_process_data, process_data, split_dataset, save_train_history
from plots import Plotter
from sub_nn import NNz

#%% ---- Set Random Seed ----
seed = 42
random.seed(seed)
np.random.seed(seed)
tf.random.set_seed(seed)

#%% ---- Load Data ----
E = 200000
dataset_name = '10'
train_data = pre_process_data(get_data(f'{dataset_name}.1', E))
val_data = pre_process_data(get_data(f'{dataset_name}.2', E))
test_data = pre_process_data(get_data(f'{dataset_name}.3', E))

# Process data (normalize)
n_train_data, n_val_data, norm_params = process_data(train_data, val_data)

#%% ---- Model Configuration ----
# Define model hyperparameters
hidden_dims = [48, 48]
learning_rate = 0.0003; n_epochs = 600; batch_size = 20
activation_function = 'leaky_relu'
lr_schedule_type = 'cosine'
loss_type = 'physics_informed'

# Create model
model_NNz = NNz(norm_params=norm_params, hidden_dims=hidden_dims, activation_func=activation_function)
print(model_NNz.summary())

#%% ---- Train Model with Physics-Informed Loss ----
# Train with physics-informed loss
history = NNz.train_model(
    model_instance=model_NNz,
    train_data=n_train_data,
    val_data=n_val_data,
    LearningRate=learning_rate, nEpochs=n_epochs, bSize=batch_size, silent_training=False,
    lr_schedule_type=lr_schedule_type, loss_type=loss_type
)

#%% ---- Save Model ----
model_path = NNz.save_model(
    model_instance=model_NNz,
    dataset_name=f'{dataset_name}',
    learning_rate=learning_rate, LR_schedule_type=lr_schedule_type, num_epochs=n_epochs,
    batch_size=batch_size, norm_params=norm_params, loss_type=loss_type,
    save_dir='../../saved_models/NNz'
)

#%% ---- Save Training History ----
# Save the training history for later analysis
save_train_history(
    history=history,
    save_path=model_path,
    filename='training_history'
)
#%% ---- Plot Training History ----
plotter = Plotter()
plotter.plot_loss(history=history, save_path=model_path)

#%% ---- Plot Loss Components ----
# Extract loss components from history
total_loss = history.history['loss']
val_total_loss = history.history['val_loss']
base_loss = history.history['base_loss_metric']
val_base_loss = history.history['val_base_loss_metric']
negative_penalty = history.history['negative_penalty_metric']
val_negative_penalty = history.history['val_negative_penalty_metric']

# Create epochs array
epochs = range(1, len(total_loss) + 1)

# Plot loss components
plt.figure(figsize=(12, 8))

# Total loss
plt.subplot(2, 2, 1)
plt.plot(epochs, total_loss, 'b-', label='Training')
plt.plot(epochs, val_total_loss, 'r-', label='Validation')
plt.title('Total Loss')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.yscale('log')
plt.legend()
plt.grid(True, linestyle='--', alpha=0.7)

# Base loss (weighted MAE)
plt.subplot(2, 2, 2)
plt.plot(epochs, base_loss, 'b-', label='Training')
plt.plot(epochs, val_base_loss, 'r-', label='Validation')
plt.title('Base Loss (Weighted MAE)')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.yscale('log')
plt.legend()
plt.grid(True, linestyle='--', alpha=0.7)

# Negative penalty
plt.subplot(2, 2, 3)
plt.plot(epochs, negative_penalty, 'b-', label='Training')
plt.plot(epochs, val_negative_penalty, 'r-', label='Validation')
plt.title('Negative Penalty')
plt.xlabel('Epoch')
plt.ylabel('Penalty')
plt.yscale('log')
plt.legend()
plt.grid(True, linestyle='--', alpha=0.7)

# Percentage of total loss from negative penalty
plt.subplot(2, 2, 4)
penalty_percentage = [100 * p / t if t > 0 else 0 for p, t in zip(negative_penalty, total_loss)]
val_penalty_percentage = [100 * p / t if t > 0 else 0 for p, t in zip(val_negative_penalty, val_total_loss)]
plt.plot(epochs, penalty_percentage, 'b-', label='Training')
plt.plot(epochs, val_penalty_percentage, 'r-', label='Validation')
plt.title('Negative Penalty (% of Total Loss)')
plt.xlabel('Epoch')
plt.ylabel('Percentage')
plt.legend()
plt.grid(True, linestyle='--', alpha=0.7)

plt.tight_layout()
plt.savefig(f'{model_path}/loss_components.png', dpi=300, bbox_inches='tight')
plt.show()

#%% ---- Test Model on Validation Data ----
# Run inference on validation data
results = model_NNz.infer(test_data)
pred_Dζ = np.array(results['summary']['pred_Dζ'])

# Plot predictions
plotter.plot_nnz_prediction(y_pred=pred_Dζ, test_data=test_data, xaxis='strain')
plotter.plot_nnz_prediction(y_pred=pred_Dζ, test_data=test_data, xaxis='increment')

#%% ---- Analyze Negative Predictions ----
# Count negative predictions
true_Dζ = test_data[:, 8]
negative_preds = pred_Dζ < 0
num_negative = np.sum(negative_preds)
percentage_negative = (num_negative / len(pred_Dζ)) * 100

print(f"\nAnalysis of Negative Predictions:")
print(f"Total predictions: {len(pred_Dζ)}")
print(f"Negative predictions: {num_negative} ({percentage_negative:.2f}%)")

# Calculate statistics for negative predictions
if num_negative > 0:
    negative_values = pred_Dζ[negative_preds]
    print(f"Min negative value: {np.min(negative_values):.8f}")
    print(f"Mean negative value: {np.mean(negative_values):.8f}")
    print(f"Max negative value: {np.max(negative_values):.8f}")

# Plot histogram of predictions
plt.figure(figsize=(10, 6))
plt.hist(pred_Dζ, bins=50, alpha=0.7)
plt.axvline(x=0, color='r', linestyle='--', label='Zero Threshold')
plt.title('Distribution of Dz Predictions')
plt.xlabel('Predicted Dz')
plt.ylabel('Frequency')
plt.legend()
plt.grid(True, linestyle='--', alpha=0.7)
plt.savefig(f'{model_path}/prediction_distribution.png', dpi=300, bbox_inches='tight')
plt.show()

#%% ---- Example: Loading Saved History ----
"""
# This is an example of how to load and use the saved history later
from funcs import load_training_history

# Load the saved history
loaded_history = load_training_history(f'{model_path}/training_history.pkl')

# Now you can access any metric from the history
epochs = range(1, len(loaded_history['loss']) + 1)
plt.figure(figsize=(10, 6))
plt.plot(epochs, loaded_history['loss'], 'b-', label='Training Loss')
plt.plot(epochs, loaded_history['val_loss'], 'r-', label='Validation Loss')
plt.title('Training and Validation Loss')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.yscale('log')
plt.legend()
plt.grid(True)
plt.show()

# You can also analyze specific metrics
if '_negative_penalty_metric' in loaded_history:
    plt.figure(figsize=(10, 6))
    plt.plot(epochs, loaded_history['_negative_penalty_metric'], 'g-', label='Negative Penalty')
    plt.title('Negative Penalty Metric')
    plt.xlabel('Epoch')
    plt.ylabel('Penalty')
    plt.yscale('log')
    plt.legend()
    plt.grid(True)
    plt.show()
"""
