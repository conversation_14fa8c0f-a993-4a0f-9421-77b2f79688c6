#%% ---- Import Required Libraries ----
import sys
import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
sys.path.append('/home/<USER>/phd/FallahnejadA/phd_gitlab/tasks/TANN_v4/src/utils')

from funcs import pre_process_data, get_data, apply_normalization
from tann import NNf
from tann_two_inputs import NNf_TwoInputs
from plots import Plotter

#%% ---- Load Models ----
tf.keras.backend.set_floatx('float64')

# Load the original three-input model
model_dir_3in = '../../saved_models/D14_LR0.0003_BS20_E2500_HL1_N(48_1)_ACTcustom_act_2025-03-28_10-51-19'
loaded_net_3in, metadata_3in = NNf.load_model_(model_dir_3in)

# Load the two-input model
model_dir_2in = '../../saved_models/D14_expoDecayLR_LR0.0003_BS20_E3500_HL1_N(48_1)_ACTcustom_act_2025-04-15_10-01-22_2inputs'
loaded_net_2in, metadata_2in = NNf_TwoInputs.load_model_(model_dir_2in)

#%% ---- Load Test Data ----
E = 200000
test_data = pre_process_data(get_data('14.3.3', E))

# Apply normalization using the parameters from each model
n_test_data_3in = apply_normalization(test_data, metadata_3in['Normalizing Parameters'])
n_test_data_2in = apply_normalization(test_data, metadata_2in['Normalizing Parameters'])

#%% ---- Sequential Prediction Debug (2-input model) ----
sequence = {'start': 0, 'length': 2000}            #TODO: Select a sequence of data points for debugging error propagation
try:
    if sequence['start'] + sequence['length'] > test_data.shape[0]:
        raise ValueError("Sequence exceeds the length of the test data.")
    sequence_data = test_data[sequence['start']:(sequence['start'] + sequence['length']), :]
except ValueError as e:
    print(f"Warning: {e}. Using the entire test dataset instead.")
    sequence_data = test_data

# Debug with predicted stress
result_iterative = loaded_net_2in.debug_sequential_predictions(sequence_data, solver='network', use_true_stress=False, console_output=False)

# Debug with true stress values
# result_true = loaded_net_2in.debug_sequential_predictions(sequence_data, solver='network', use_true_stress=True, console_output=False)

plotter = Plotter()
# plotter.plot_prediction(y_pred=np.array(result_iterative['summary']['pred_F']), y_true=np.array(result_iterative['summary']['true_F']), title='F_tdt')
plotter.plot_prediction(y_pred=np.array(result_iterative['summary']['pred_F']), test_data=test_data, title='F_tdt', xaxis='increment')
plotter.plot_prediction(y_pred=np.array(result_iterative['summary']['pred_σ']), test_data=test_data, title='σ_tdt', xaxis='increment')

# loaded_net_2in.plot_error_propagation(result_iterative, result_true, save_dir='logs', show_plot=True)
#%% ---- Test Networks with Controlled Inputs ----
# Test with variable strain (Dε) and constant stress
# strain_range = (-0.00002, 0.00002)
# constant_stress = 10.0
# num_points = 50

# Test three-input model
# results_3in = loaded_net_3in.test_controlled_inputs(
#     variable_param='strain',
#     constant_values={'stress': constant_stress, 'dz': 0.0},
#     variable_range=strain_range,
#     num_points=num_points,
#     solver='network',
#     console_output=False,
#     plot_results=True
# )

# Test two-input model
# results_2in = loaded_net_2in.test_controlled_inputs(
#     variable_param='strain',
#     constant_values={'stress': constant_stress, 'dz': 0.0},
#     variable_range=strain_range,
#     num_points=num_points,
#     solver='network',
#     console_output=False,
#     plot_results=True
# )

#%% ---- Compare Models ----
# Extract data
# strain_values = np.array(results_3in['inputs']['strain'])
# F_3in = np.array(results_3in['predictions']['F'])
# σ_3in = np.array(results_3in['predictions']['σ'])
# F_2in = np.array(results_2in['predictions']['F'])
# σ_2in = np.array(results_2in['predictions']['σ'])
# F_analytic = np.array(results_3in['analytical']['F'])
# σ_analytic = np.array(results_3in['analytical']['σ'])

# # Calculate errors
# F_error_3in = (F_3in - F_analytic)
# σ_error_3in = (σ_3in - σ_analytic)
# F_error_2in = (F_2in - F_analytic)
# σ_error_2in = (σ_2in - σ_analytic)

# # Plot comparison
# plt.figure(figsize=(15, 10))

# # Plot F values
# plt.subplot(2, 2, 1)
# plt.plot(strain_values, F_3in, 'r-', label='Three-Input Model')
# plt.plot(strain_values, F_2in, 'g--', label='Two-Input Model')
# plt.plot(strain_values, F_analytic, 'b:', label='Analytical')
# plt.title('Free Energy')
# plt.xlabel(r'$\Delta\varepsilon$')
# plt.ylabel(r'$F_{t+1}$')
# plt.legend(); plt.ticklabel_format(style='sci', axis='both', scilimits=(0, 2))
# plt.grid(True, linestyle='--', alpha=0.5)

# # Plot σ values
# plt.subplot(2, 2, 2)
# plt.plot(strain_values, σ_3in, 'r-', label='Three-Input Model')
# plt.plot(strain_values, σ_2in, 'g--', label='Two-Input Model')
# plt.plot(strain_values, σ_analytic, 'b:', label='Analytical')
# plt.title('Stress')
# plt.xlabel(r'$\Delta\varepsilon$')
# plt.ylabel(r'$\sigma_{t+1}$')
# plt.legend(); plt.ticklabel_format(style='sci', axis='both', scilimits=(0, 2))
# plt.grid(True, linestyle='--', alpha=0.5)

# # Plot F errors
# plt.subplot(2, 2, 3)
# plt.plot(strain_values, F_error_3in, 'r-', label='Three-Input Model')
# plt.plot(strain_values, F_error_2in, 'g--', label='Two-Input Model')
# plt.title('Free Energy Error')
# plt.xlabel(r'$\Delta\varepsilon$')
# plt.ylabel('Error')
# plt.legend(); plt.ticklabel_format(style='sci', axis='both', scilimits=(0, 2))
# plt.grid(True, linestyle='--', alpha=0.5)

# # Plot σ errors
# plt.subplot(2, 2, 4)
# plt.plot(strain_values, σ_error_3in, 'r-', label='Three-Input Model')
# plt.plot(strain_values, σ_error_2in, 'g--', label='Two-Input Model')
# plt.title('Stress Error')
# plt.xlabel(r'$\Delta\varepsilon$')
# plt.ylabel('Error')
# plt.legend(); plt.ticklabel_format(style='sci', axis='both', scilimits=(0, 2))
# plt.grid(True, linestyle='--', alpha=0.5)

# plt.tight_layout()
# plt.savefig('logs/model_comparison.png', dpi=300, bbox_inches='tight')
# plt.show()
#%% ---- Test Network with Variable Dζ (2-input model) ----
# Test with variable Dζ, constant strain and stress
# variable_dz_results = loaded_net_2in.test_network_with_variable_dz(
#     constant_stress=25.0,  # MPa
#     constant_strain=0.00000,   # strain
#     dz_range=(-0.0001, 0.0001),   # Dζ range
#     num_points=11,
#     solver='network',
#     console_output=False,
#     plot_results=True
# )
#%%
# variable_dz_results_3in = loaded_net_3in.test_network_with_variable_dz(
#     constant_stress=25.0,  # MPa
#     constant_strain=0.00001,   # strain
#     dz_range=(-0.0001, 0.0001),   # Dζ range
#     num_points=11,
#     solver='network',
#     console_output=False,
#     plot_results=True
# )

# variable_dz_results_2in = loaded_net_2in.test_network_with_variable_dz(
#     constant_stress=25.0,  # MPa
#     constant_strain=0.00001,   # strain
#     dz_range=(-0.0001, 0.0001),   # Dζ range
#     num_points=11,
#     solver='network',
#     console_output=False,
#     plot_results=True
# )
