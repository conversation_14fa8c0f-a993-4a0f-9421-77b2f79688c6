# Running Random Search for NNz on GPU Clusters

This document explains how to run the `random_search_nnz.py` script on GPU clusters for hyperparameter optimization of the NNz model.

## Prerequisites

- TensorFlow 2.x
- CUDA and cuDNN installed on the cluster
- Access to a GPU node

## Command Line Arguments

The script supports the following command line arguments:

- `--gpu_mem`: GPU memory limit in MB (default: no limit)
- `--allow_growth`: Allow GPU memory growth (flag, no value needed)
- `--num_trials`: Number of random trials to run (default: 15)
- `--seed`: Random seed for reproducibility (default: 42)

## Example Usage

### Basic Usage with Default Settings

```bash
python random_search_nnz.py
```

### Limiting GPU Memory

To limit GPU memory usage to 4GB (4096MB):

```bash
python random_search_nnz.py --gpu_mem 4096
```

### Allowing Memory Growth

To allow GPU memory to grow as needed (recommended for shared GPU environments):

```bash
python random_search_nnz.py --allow_growth
```

### Running More Trials

To run 30 random search trials instead of the default 15:

```bash
python random_search_nnz.py --num_trials 30
```

### Combining Options

You can combine multiple options:

```bash
python random_search_nnz.py --gpu_mem 6144 --allow_growth --num_trials 50 --seed 123
```

## Running on a Cluster with SLURM

Here's an example SLURM script to run the random search on a GPU node:

```bash
#!/bin/bash
#SBATCH --job-name=nnz_search
#SBATCH --output=nnz_search_%j.out
#SBATCH --error=nnz_search_%j.err
#SBATCH --time=24:00:00
#SBATCH --nodes=1
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --gres=gpu:1
#SBATCH --mem=16G

# Load necessary modules (adjust as needed for your cluster)
module load cuda/11.2
module load python/3.8

# Activate virtual environment if needed
# source /path/to/your/venv/bin/activate

# Navigate to the script directory
cd /path/to/TANN_v4/src/interfaces

# Run the script with desired options
python random_search_nnz.py --gpu_mem 8192 --allow_growth --num_trials 100 --seed 42
```

Save this script as `run_nnz_search.sh` and submit it with:

```bash
sbatch run_nnz_search.sh
```

## Output

The script creates a timestamped directory in `../../results/nnz_random_search_TIMESTAMP/` containing:

- `nnz_random_search_results.json`: JSON file with detailed results of all trials
- `top5_comparison.png`: Plot comparing validation loss curves of the top 5 configurations
- `loss_trial_X.png`: Loss curves for each successful trial
- `predictions_trial_X.png`: Prediction plots for each successful trial
- `models/`: Directory containing saved models for the best configurations

## Notes

- The script automatically detects if a GPU is available and falls back to CPU if none is found
- When running on a cluster, plots are saved but not displayed
- Results are saved in a timestamped directory to avoid overwriting previous runs
