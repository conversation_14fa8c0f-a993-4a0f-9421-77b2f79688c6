#%% ---- Import Required Libraries ----
import sys

import matplotlib.pyplot as plt
import numpy as np
import tensorflow as tf

sys.path.append('/home/<USER>/phd/FallahnejadA/phd_gitlab/tasks/TANN_v4/src/utils')

from funcs import get_data, pre_process_data, process_data
from plots import Plotter
from tann_two_inputs import NNf_TwoInputs

#%% ---- Set Random Seed ----
seed = 42
tf.random.set_seed(seed)
np.random.seed(seed)

#%% ---- Set Floating Point Precision ----
tf.keras.backend.set_floatx('float64')

#%% ---- Load and Preprocess Data ----
E = 200000
dataset_name = '14'

train_data = pre_process_data(get_data(f'{dataset_name}.1', E))
val_data = pre_process_data(get_data(f'{dataset_name}.2', E))

n_train_data, n_val_data, norm_params = process_data(train_data, val_data)

#%% ---- Model Configuration ----
hidden_dims = [48]
learningRate=3e-4; nEpochs = 3500; bSize = 20

model_two_inputs = NNf_TwoInputs(norm_params=norm_params, hidden_dims=hidden_dims, activation_func='custom_act')
print(model_two_inputs.summary())

# Learning rate schedule type: 'exponential', 'cosine', or 'constant'
lr_schedule_type = 'exponential'  # Change this to try different schedules

print(f"\nTraining two-input model with {lr_schedule_type} learning rate schedule...")
history = NNf_TwoInputs.train_model(
    model_two_inputs,
    train_data=n_train_data,
    val_data=n_val_data,
    LearningRate=learningRate,
    nEpochs=nEpochs,
    bSize=bSize,
    silent_training=False,
    lr_schedule_type=lr_schedule_type
)

#%% ---- Plot Training History ----
plt.figure(figsize=(12, 5))

# Plot training & validation loss
plt.subplot(1, 2, 1)
plt.plot(history.history['loss'])
plt.plot(history.history['val_loss'])
plt.title('Model Loss (Two-Input)')
plt.ylabel('Loss')
plt.xlabel('Epoch')
plt.legend(['Train', 'Validation'], loc='upper right')
plt.grid(True, linestyle='--', alpha=0.7)

# Plot training & validation MAPE
plt.subplot(1, 2, 2)
plt.plot(history.history['mape'])
plt.plot(history.history['val_mape'])
plt.title('Model MAPE (Two-Input)')
plt.ylabel('MAPE')
plt.xlabel('Epoch')
plt.legend(['Train', 'Validation'], loc='upper right')
plt.grid(True, linestyle='--', alpha=0.7)

plt.tight_layout()
plt.savefig(f'logs/two_input_model_training_history_{dataset_name}.png', dpi=300, bbox_inches='tight')
plt.show()

#%% ---- Save Model ----
model_path = NNf_TwoInputs.save_model(
    model_instance=model_two_inputs,
    dataset_name=dataset_name,
    learning_rate=learningRate,
    num_epochs=nEpochs,
    batch_size=bSize,
    norm_params=norm_params,
    save_dir='../../saved_models'
)

print(f"Two-input model saved to: {model_path}")

# %% Plot
plotter = Plotter()
plotter.plot_loss(history=history, save_path=model_path)
