"""
This script serves as the test interface for the TANN-z model.

Date: 2025-05-02
Author: <PERSON><PERSON><PERSON>
"""
#%% ---- Import Required Libraries ----
import sys
import numpy as np
import tensorflow as tf
from keras.utils import custom_object_scope
from pathlib import Path

sys.path.append('/home/<USER>/phd/FallahnejadA/phd_gitlab/tasks/TANN_v4/src/utils')

from funcs import get_data, pre_process_data, split_dataset
from plots import Plotter
from sub_nn import NNz, NNzWithClassifier
from classifier import DzBinaryClassifier

#%% ---- Load Model ----
tf.keras.backend.set_floatx('float64')

#TODO: Specify the model directory
base_dir = Path('../../saved_models')
model_dir = base_dir / 'NNz' / 'D10.00_LR0.0003_Schedulecosine_BS20_E3500_HL2_N(48_48_1)_ACTleaky_relu_LossWM_2025-06-03_15-04-32'
with custom_object_scope({'_weighted_mae_loss': NNz._weighted_mae_loss}):
    loaded_net, metadata = NNz.load_model_(model_dir)

# classifier_dir = base_dir / 'binary_classifier'
# classifier = DzBinaryClassifier.load(classifier_dir)

# nnz_with_classifier = NNzWithClassifier(
#     norm_params=loaded_net.norm_params,
#     hidden_dims=[layer.units for layer in loaded_net.hidden_layers],
#     activation_func=loaded_net.activation_func,
#     classifier=classifier,
#     classifier_method='hybrid'  # Try different methods: 'hard', 'soft', 'adaptive', 'hybrid'
# )
# nnz_with_classifier.set_weights(loaded_net.get_weights())

#%% ---- Load Test Data ----
E = 200000
test_data = pre_process_data(get_data('10.3', E))           #TODO: specify the test dataset

# _, _, test_data = split_dataset('10.00', E, train_size=1500, val_size=750, test_size=750,
#                               random_state=42, plot_distributions=False)

#%% ---- Run Inference ----
# results = nnz_with_classifier.infer(test_data)
results = loaded_net.infer(test_data)
pred_Dζ = np.array(results['summary']['pred_Dζ'])

#%% ---- Plot Predictions ----
plotter = Plotter()
plotter.plot_nnz_prediction(y_pred=pred_Dζ, test_data=test_data, xaxis='strain')


#%% ---- Print summary statistics ----
# if 'summary' in results:
#     print(f"\n===== NNz Inference Results Summary =====")
#     print(f"Number of samples: {results['num_samples']}")
#     print(f"Average Dζ Error: {results['summary']['avg_Dζ_error_percent']:.4f}%")
#     print(f"Max Dζ Error: {results['summary']['max_Dζ_error_percent']:.4f}%")

