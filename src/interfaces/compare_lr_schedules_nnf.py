"""
This script compares the performance of NNf models trained with different learning rate schedules.

Date: 2025-05-14
Author: <PERSON><PERSON><PERSON>
"""

#%% ---- Import Required Libraries ----
import random
import sys

import matplotlib.pyplot as plt
import numpy as np
import tensorflow as tf

sys.path.append('/home/<USER>/phd/FallahnejadA/phd_gitlab/tasks/TANN_v4/src/utils')

from funcs import get_data, pre_process_data, process_data
from tann import NNf

#%% ---- Set Random Seed ----
seed = 42
random.seed(seed)
tf.random.set_seed(seed)
np.random.seed(seed)

#%% ---- Set Floating Point Precision ----
tf.keras.backend.set_floatx('float64')

#%% ---- Load and Preprocess Data ----
E = 200000
dataset_name = '10'

train_data = pre_process_data(get_data(f'{dataset_name}.1', E))
val_data = pre_process_data(get_data(f'{dataset_name}.2', E))

n_train_data, n_val_data, norm_params = process_data(train_data, val_data)

#%%
activation_dict = {
    'custom_act': 'custom_act',
    'relu': tf.keras.activations.relu,
    'leaky_relu': tf.keras.layers.LeakyReLU(),
    'sigmoid': tf.keras.activations.sigmoid,
    'tanh': tf.keras.activations.tanh,
    'elu': tf.keras.activations.elu,
}

#%% ---- Model Configuration ----
hidden_dims = [48]
learningRate = 3e-4; nEpochs = 3500; bSize = 20
activation_function = activation_dict['custom_act']

#%% ---- Train Models with Different Learning Rate Schedules ----
# NOTE: The learning rate schedules are configured in tann.py with the following parameters:
# - Exponential: decay_steps = nEpochs // 2, decay_rate = 0.75
# - Cosine: first_decay_steps = nEpochs // 5, t_mul = 2.0, m_mul = 0.9, alpha = 1e-5
# - Constant: Uses the fixed learning rate value
lr_schedules = ['exponential', 'constant', 'cosine']
histories = {}

for lr_schedule in lr_schedules:
    print(f"\n{'='*50}")
    print(f"Training model with {lr_schedule} learning rate schedule...")
    print(f"{'='*50}")

    model = NNf(norm_params=norm_params, hidden_dims=hidden_dims, activation_func=activation_function)

    history = NNf.train_model(
        model,
        train_data=n_train_data,
        val_data=n_val_data,
        LearningRate=learningRate, nEpochs=nEpochs, bSize=bSize, silent_training=False,
        lr_schedule_type=lr_schedule
    )

    histories[lr_schedule] = history

#%% ---- Compare Training Histories ----
# Create directory for plots if it doesn't exist
import os
os.makedirs('logs', exist_ok=True)

# Plot training loss comparison
plt.rcParams["font.family"] = "serif"
plt.rc('axes.formatter', use_mathtext=True)
plt.rcParams["font.serif"] = "cmr10"
plt.rcParams['font.size']=12
fig, ax = plt.subplots(figsize=(6, 5), tight_layout=True)

for lr_schedule, history in histories.items():
    ax.semilogy(range(1, len(history.history['loss']) + 1),history.history['loss'], label=f'{lr_schedule.title()}-train')
    ax.scatter(range(1, len(history.history['val_loss']) + 1),history.history['val_loss'], marker='o', s=7.5, alpha=.65,label=f'{lr_schedule.title()}-validation')
ax.set_title('Training Loss Comparison')
ax.set_ylabel('Loss (MAE)')
ax.set_xlabel('Epoch')
ax.legend(loc='lower left', frameon=True, fontsize="9")
ax.set_yscale('log'); ax.set_xscale('log')
ax.grid(True, linestyle='--', alpha=0.5)

plt.savefig(f'logs/lr_schedule_comparison_{dataset_name}.png', dpi=500, bbox_inches='tight')
plt.show()

# Print final loss values
print("\nFinal Loss Values:")
print("-" * 50)
print(f"{'Schedule':<15} {'Training Loss':<20} {'Validation Loss':<20}")
print("-" * 50)
for lr_schedule, history in histories.items():
    train_loss = history.history['loss'][-1]
    val_loss = history.history['val_loss'][-1]
    print(f"{lr_schedule:<15} {train_loss:<20.6f} {val_loss:<20.6f}")
print("-" * 50)

# Find the best model based on validation loss
best_schedule = min(histories.keys(), key=lambda k: min(histories[k].history['val_loss']))
best_val_loss = min(histories[best_schedule].history['val_loss'])
best_epoch = histories[best_schedule].history['val_loss'].index(best_val_loss)

print(f"\nBest model: {best_schedule} learning rate schedule")
print(f"Best validation loss: {best_val_loss:.6f} at epoch {best_epoch+1}")
