"""
This script serves as the test interface for the complete TANN model.

Date: 2025-05-13
Author: <PERSON><PERSON><PERSON>
"""

#%% ---- Import Required Libraries ----
import sys

import numpy as np
import tensorflow as tf

sys.path.append('/home/<USER>/phd/FallahnejadA/phd_gitlab/tasks/TANN_v4/src/utils')

from funcs import get_data, pre_process_data
from plots import Plotter
from tann import TANN, TANNWithClassifier

#%% ---- Load sub-Models ----
tf.keras.backend.set_floatx('float64')

# Load NNz and NNf models
NNz_model_dir = '../../saved_models/NNz/D10_LR0.0003_Schedulecosine_BS20_E3500_HL2_N(48_48_1)_ACTleaky_relu_LossWM_2025-05-16_15-07-15_OKAY'
NNf_model_dir = '../../saved_models/NNf/D10_expoDecayLR_LR0.0003_BS20_E3500_HL1_N(48_1)_ACTcustom_act_2025-04-15_10-42-57'
classifier_dir = '../../saved_models/binary_classifier'

TANN_model_with_classifier = TANNWithClassifier(NNz_model_dir, NNf_model_dir, classifier_dir, classifier_method='adaptive')
TANN_model = TANN(NNz_model_dir, NNf_model_dir)


#%% ---- Load Test Data ----
E = 200000
test_data = pre_process_data(get_data('12.5', E))
model = TANN_model

#%% ---- Run Inference ----
results = model.infer(input_test=test_data)
pred_D_eq_pl_ε = np.array(results['summary']['pred_D_eq_pl_ε'])
pred_F = np.array(results['summary']['pred_F'])
pred_σ = np.array(results['summary']['pred_σ'])

#%% ---- Plot Predictions ----
plotter = Plotter()
plotter.plot_nnz_prediction(y_pred=pred_D_eq_pl_ε, test_data=test_data, xaxis='strain')
plotter.plot_prediction(y_pred=pred_F, test_data=test_data, title='F_tdt', xaxis='strain')
plotter.plot_prediction(y_pred=pred_σ, test_data=test_data, title='σ_tdt', xaxis='strain')

#%% ---- Print summary statistics ----
# if 'summary' in results:
#     print(f"\n===== TANN Inference Results Summary =====")
#     print(f"Number of samples: {results['num_samples']}")
#     print(f"Average D_acc_eq_pl_ε Error: {results['summary']['avg_D_acc_eq_pl_ε_error_percent']:.4f}%")
#     print(f"Average F Error: {results['summary']['avg_F_error_percent']:.4f}%")
#     print(f"Average σ Error: {results['summary']['avg_σ_error_percent']:.4f}%")
#     print(f"Max D_acc_eq_pl_ε Error: {results['summary']['max_D_acc_eq_pl_ε_error_percent']:.4f}%")
#     print(f"Max F Error: {results['summary']['max_F_error_percent']:.4f}%")
#     print(f"Max σ Error: {results['summary']['max_σ_error_percent']:.4f}%")

#%% ---- Compare Predictions With and Without Classifier ----
# Call the comparison method

# comparison = TANN_model_with_classifier.compare_with_without_classifier(test_data, visualize=True)
