"""
Train a binary classifier to predict whether Δζ should be zero or non-zero.

This script trains a binary classifier that can be used alongside the NNz regression model
to improve predictions for zero-valued targets.

Date: 2025-05-14
Author: <PERSON><PERSON><PERSON>ahnej<PERSON>
"""
#%% ---- Import Required Libraries ----
import sys
import numpy as np
import tensorflow as tf
import matplotlib.pyplot as plt
from sklearn.metrics import confusion_matrix, classification_report

sys.path.append('/home/<USER>/phd/FallahnejadA/phd_gitlab/tasks/TANN_v4/src/utils')

from funcs import get_data, pre_process_data, process_data, split_dataset
from classifier import DzBinaryClassifier, process_data_for_classifier
from plots import Plotter

#%% ---- Set Random Seed ----
seed = 42
np.random.seed(seed)
tf.random.set_seed(seed)

#%% ---- Load Data ----
E = 200000
dataset_name = '10'
train_data = pre_process_data(get_data(f'{dataset_name}.1', E))
val_data = pre_process_data(get_data(f'{dataset_name}.2', E))
test_data = pre_process_data(get_data(f'{dataset_name}.3', E))

# Prepare data for binary classifier
X_train, y_train = process_data_for_classifier(train_data)
X_val, y_val = process_data_for_classifier(val_data)
X_test, y_test = process_data_for_classifier(test_data)

# Print class distribution
print(f"Training set: {len(y_train)} samples, {np.sum(y_train)} non-zero ({np.sum(y_train)/len(y_train)*100:.2f}%)")
print(f"Validation set: {len(y_val)} samples, {np.sum(y_val)} non-zero ({np.sum(y_val)/len(y_val)*100:.2f}%)")
print(f"Test set: {len(y_test)} samples, {np.sum(y_test)} non-zero ({np.sum(y_test)/len(y_test)*100:.2f}%)")

#%% ---- Create and Train Binary Classifier ----
# Create binary classifier
binary_classifier = DzBinaryClassifier(hidden_dims=[64, 32], dropout_rate=0.3)

# Train the classifier
history = binary_classifier.train(
    X_train, y_train,
    X_val=X_val, y_val=y_val,
    epochs=500,
    batch_size=32,
    patience=30
)

#%% ---- Optimize Threshold ----
# Find the optimal threshold using validation data
optimal_threshold = binary_classifier.optimize_threshold(X_val, y_val, metric='f1')

#%% ---- Evaluate on Test Set ----
# Make predictions on test set
y_pred, y_prob = binary_classifier.predict(X_test, threshold=optimal_threshold)

# Print classification report
print("\nClassification Report:")
print(classification_report(y_test, y_pred))

# Print confusion matrix
cm = confusion_matrix(y_test, y_pred)
print("\nConfusion Matrix:")
print(cm)

# Calculate accuracy for zero and non-zero classes separately
zero_accuracy = cm[0, 0] / (cm[0, 0] + cm[0, 1]) if (cm[0, 0] + cm[0, 1]) > 0 else 0
nonzero_accuracy = cm[1, 1] / (cm[1, 0] + cm[1, 1]) if (cm[1, 0] + cm[1, 1]) > 0 else 0

print(f"\nZero class accuracy: {zero_accuracy:.4f}")
print(f"Non-zero class accuracy: {nonzero_accuracy:.4f}")

#%% ---- Plot Training History ----
plt.figure(figsize=(10, 4))
plt.subplot(1, 2, 1)
plt.plot(history.history['loss'], label='Training Loss')
plt.plot(history.history['val_loss'], label='Validation Loss')
plt.title('Loss')
plt.xlabel('Epoch')
plt.ylabel('Binary Crossentropy'); plt.yscale('log')
plt.legend()

plt.subplot(1, 2, 2)
plt.plot(history.history['accuracy'], label='Training Accuracy')
plt.plot(history.history['val_accuracy'], label='Validation Accuracy')
plt.title('Accuracy')
plt.xlabel('Epoch')
plt.ylabel('Accuracy')
plt.legend()

plt.tight_layout()
plt.show()

#%% ---- Plot ROC Curve ----
from sklearn.metrics import roc_curve, auc

# Calculate ROC curve
fpr, tpr, thresholds = roc_curve(y_test, y_prob)
roc_auc = auc(fpr, tpr)

# Plot ROC curve
plt.figure(figsize=(8, 6))
plt.plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC curve (area = {roc_auc:.2f})')
plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
plt.xlim([0.0, 1.0])
plt.ylim([0.0, 1.05])
plt.xlabel('False Positive Rate')
plt.ylabel('True Positive Rate')
plt.title('Receiver Operating Characteristic')
plt.legend(loc="lower right")
plt.grid(True, linestyle='--', alpha=0.7)
plt.show()

#%% ---- Save the Model ----
# save_dir = '../../saved_models/binary_classifier'
# binary_classifier.save(save_dir)
# print(f"Binary classifier saved to {save_dir}")
