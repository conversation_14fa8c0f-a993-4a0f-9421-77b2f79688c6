# TANN_v4: Thermodynamics-based Artificial Neural Network

A TensorFlow-based implementation of a thermodynamics-based artificial neural network for material modeling.

## Overview

This repository contains the implementation of a TensorFlow-based neural network model for predicting free energy, `F_tdt`, having the strain increment, `Dε`, stress, `σ_t`, and the increment of the state variable, `Dζ`. The model derives the next stress, `σ_tdt` by taking the derivative of the predicted free energy with respect to strain increment, either using automatic differentiation or analytical methods.

The project now supports both three-input (Dε, σ_t, Dζ) and two-input (Dε, σ_t) model variants, with the latter being useful for cases where the third input (Dζ) is always zero.

## Project Structure

```
TANN_v4/
├── data/                    # Training and validation datasets
│   ├── 10.1/               # Dataset folders
│   ├── 10.2/
│   └── ...
├── saved_models/           # Saved model checkpoints
├── src/
│   ├── debug/              # Debugging utilities
│   │   └── debug.py        # Debug script for controlled tests
│   ├── interfaces/         # Training and testing interfaces
│   │   ├── train.py        # Training script for 3-input model
│   │   ├── train_two_inputs.py  # Training script for 2-input model
│   │   ├── test.py         # Testing/inference script for 3-input model
│   │   ├── test_two_inputs.py   # Testing script for 2-input model
│   │   ├── compare_lr_schedules.py  # Learning rate comparison script
│   │   └── inp_file_runner.py  # Input file processor
│   └── utils/              # Utility modules
│       ├── funcs.py        # General utility functions
│       ├── tann.py         # Core TANN model implementation (3-input)
│       ├── tann_debug.py   # Debug version of TANN model
│       └── plots.py        # Visualization utilities
```

## Features

- Thermodynamically consistent neural network architecture
- Support for both three-input and two-input model variants
- Advanced learning rate scheduling (constant, exponential decay, cosine decay)
- Custom activation function (ELU(x²))
- Automatic normalization and standardization of input/output data
- Support for both network-based and analytical solvers
- Comprehensive plotting utilities for model evaluation
- Detailed inference results with error metrics and step-by-step analysis
- Model saving and loading with metadata preservation

## Requirements

- Python 3.x
- TensorFlow 2.x
- NumPy
- Matplotlib

## Functions and Classes

### Core Classes

#### `NNf` (src/utils/tann.py)
Main three-input neural network model class inheriting from `tf.keras.Model`.

```python
NNf(norm_params, hidden_dims, activation_func='custom_act')
```
- **Methods**:
  - `call(inputs)`: Forward pass through the network
  - `get_analytic_F(input)`: Computes analytical free energy
  - `get_stress(input, solver='network')`: Computes stress using either network or analytical solver
  - `infer(inputs, true_data=False, solver='network')`: Performs inference on a sequence of inputs and returns detailed results dictionary
  - `train_model(model_instance, train_data, val_data, ..., lr_schedule_type='constant')`: Class method for model training with learning rate scheduling
  - `save_model(model_instance, ...)`: Saves model with metadata
  - `load_model_(model_dir)`: Loads model and metadata

#### `NNf_TwoInputs` (src/utils/tann_two_inputs.py)
Two-input variant of the neural network model.

```python
NNf_TwoInputs(norm_params, hidden_dims, activation_func='custom_act')
```
- **Methods**:
  - Similar to `NNf` but designed for two inputs (Dε, σ_t) instead of three
  - Includes the same learning rate scheduling options

### Utility Functions (src/utils/funcs.py)

#### Data Processing
```python
get_data(folder_name, E)
```
- Reads raw data from specified folder
- Returns numpy array with strain, stress, state variable, and free energy

```python
pre_process_data(data)
```
- Processes raw data into network inputs/outputs
- Returns array with [Dε, σ_t, sgn(Dε)*Dζ, F_tdt, σ_tdt]

```python
process_data(train_data, val_data, test_data)
```
- Normalizes datasets
- Returns normalized data and normalization parameters

#### Data Normalization
```python
normalize_data(data)
```
- Normalizes data to [-1, 1] range
- Returns normalized data and parameters

```python
get_α_β(data, norm=True, norm_01=False, no_norm=False)
```
- Computes normalization parameters (α & β)
- Supports different normalization schemes

#### Error Metrics
```python
compute_error(y_pred, y_true)
```
- Computes various error metrics (MAPE, MAE, MSE, RMSE)
- Returns dictionary with error arrays and averages

```python
compute_r2_score(y_pred, y_true)
```
- Computes R² score between predicted and true values

#### Learning Rate Scheduling
```python
compare_learning_rate_schedules(model_class, dataset_name, hidden_dims, learning_rate, n_epochs, batch_size, schedules=None)
```
- Trains models with different learning rate schedules and compares their performance
- Returns histories, best schedule, and best validation loss

```python
train_with_schedule(model_class, train_data, val_data, norm_params, hidden_dims, lr_schedule_type, learning_rate, n_epochs, batch_size)
```
- Trains a model with a specific learning rate schedule
- Returns training history and trained model

```python
visualize_training_results(histories, dataset_name, log_scale=True)
```
- Creates visualizations comparing different learning rate schedules
- Returns matplotlib figure object

### Plotting Utilities (src/utils/plots.py)

#### `Plotter` Class
- Methods for visualizing:
  - `plot_loss`: Training history and loss curves
  - `plot_prediction`: Predictions vs ground truth with error metrics
  - `plot_data_scatter`: 3D scatter plots of input-output relationships
  - `plot_data_histograms`: Distribution of data features
  - `plot_feature_correlations`: Correlation between different features
  - `plot_learning_rate_schedules`: Comparison of different learning rate schedules

### Custom Activation

```python
custom_act(x)
```
- Custom activation function: ELU(x²)
- Registered with Keras custom objects

## Data Formats

### Input Data Format
- Strain (ε): Shape (n, 1)
- Stress (σ): Shape (n, 1)
- State variable (ζ): Shape (n, 1)

### Preprocessed Data Format
- Dε: Strain increment
- σ_t: Current stress
- sgn(Dε)*Dζ: Signed state variable increment
- F_tdt: Free energy at t+dt
- σ_tdt: Stress at t+dt

### Model Input/Output

#### Three-Input Model
- **Input**: [Dε, σ_t, sgn(Dε)*Dζ]
- **Output**: F_tdt (primary), σ_tdt (derived)

#### Two-Input Model
- **Input**: [Dε, σ_t]
- **Output**: F_tdt (primary), σ_tdt (derived)

## Usage

### Training with Learning Rate Scheduling

```python
from utils.tann import NNf
from utils.funcs import get_data, pre_process_data, process_data

# Load and preprocess data
E = 200000
dataset_name = '10'
train_data = pre_process_data(get_data(f'{dataset_name}.1', E))
val_data = pre_process_data(get_data(f'{dataset_name}.2', E))

# Process data and get normalization parameters
n_train_data, n_val_data, norm_params = process_data(train_data, val_data)

# Create and train model with exponential learning rate decay
hidden_dims = [48]
model = NNf(norm_params=norm_params, hidden_dims=hidden_dims, activation_func='custom_act')
history = NNf.train_model(
    model,
    train_data=n_train_data,
    val_data=n_val_data,
    LearningRate=3e-4,
    nEpochs=3500,
    bSize=20,
    lr_schedule_type='exponential'  # Options: 'constant', 'exponential', 'cosine'
)
```

### Training Two-Input Model

```python
from utils.tann_two_inputs import NNf_TwoInputs
from utils.funcs import get_data, pre_process_data, process_data

# Load and preprocess data
E = 200000
dataset_name = '14'
train_data = pre_process_data(get_data(f'{dataset_name}.1', E))
val_data = pre_process_data(get_data(f'{dataset_name}.2', E))

# Process data and get normalization parameters
n_train_data, n_val_data, norm_params = process_data(train_data, val_data)

# Create and train two-input model
hidden_dims = [48]
model = NNf_TwoInputs(norm_params=norm_params, hidden_dims=hidden_dims, activation_func='custom_act')
history = NNf_TwoInputs.train_model(
    model,
    train_data=n_train_data,
    val_data=n_val_data,
    LearningRate=3e-4,
    nEpochs=3500,
    bSize=20,
    lr_schedule_type='exponential'
)
```

### Comparing Learning Rate Schedules

```python
from utils.funcs import compare_learning_rate_schedules
from utils.tann import NNf

# Compare different learning rate schedules
histories, best_schedule, best_val_loss = compare_learning_rate_schedules(
    model_class=NNf,
    dataset_name='10',
    hidden_dims=[48],
    learning_rate=3e-4,
    n_epochs=3500,
    batch_size=20,
    schedules=['constant', 'exponential', 'cosine']
)

print(f"Best schedule: {best_schedule} with validation loss {best_val_loss:.6f}")
```

### Testing and Inference

```python
from utils.tann import NNf
from utils.funcs import pre_process_data, get_data
from utils.plots import Plotter

# Load saved model
model_dir = 'path/to/saved/model'
loaded_model, metadata = NNf.load_model_(model_dir)

# Run inference with detailed results
E = 200000
test_data = pre_process_data(get_data('10.3', E))
results = loaded_model.infer(test_data, true_data=False, solver='analytic')

# Extract predictions and plot results
F_tdt = np.array(results['summary']['pred_F'])
σ_tdt = np.array(results['summary']['pred_σ'])

plotter = Plotter()
plotter.plot_prediction(y_pred=F_tdt, y_true=test_data[:, 3], title='F_tdt')
plotter.plot_prediction(y_pred=σ_tdt, y_true=test_data[:, 4], title='σ_tdt')

# Access detailed error metrics
print(f"Average F Error: {results['summary']['avg_F_error_percent']:.4f}%")
print(f"Average σ Error: {results['summary']['avg_σ_error_percent']:.4f}%")
```

## Model Architecture

### Three-Input Model (NNf)

The standard TANN model implements a thermodynamics-based architecture that:
- Takes strain increment (Dε), stress (σ_t), and state variable increment (Dζ) as inputs
- Predicts free energy (F_tdt) directly and stress (σ_tdt) through automatic differentiation
- Ensures thermodynamic consistency through its architecture
- Uses a custom activation function ELU(x²) for enhanced performance

### Two-Input Model (NNf_TwoInputs)

The two-input variant simplifies the model for cases where Dζ is always zero:
- Takes only strain increment (Dε) and stress (σ_t) as inputs
- Maintains the same thermodynamic consistency as the three-input model
- Reduces potential information leakage when the third input is not relevant
- Uses the same custom activation function and architectural principles

### Learning Rate Scheduling

The models support three types of learning rate schedules:

1. **Constant**: Fixed learning rate throughout training
2. **Exponential Decay**: Gradually reduces learning rate over time
   - Starts with higher learning rate for rapid initial convergence
   - Decays to lower rates for fine-tuning
3. **Cosine Decay with Restarts**: Cyclical learning rate schedule
   - Periodically restarts from higher learning rates
   - Helps escape local minima and explore parameter space more effectively

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Authors

- Alireza Fallahnejad
