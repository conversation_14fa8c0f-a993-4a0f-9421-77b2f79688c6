# NNz Unified Loss Function Guide

## Overview

The NNz class now features a unified loss function that combines weighted Mean Absolute Error (MAE) with physics-informed penalties. This approach ensures that the equivalent plastic strain increments (Δζ) predictions are physically meaningful (non-negative) while maintaining good model performance.

## Key Features

### 1. Unified Architecture
- **Single Loss Function**: `_loss_function()` combines all loss components
- **Comprehensive Metrics**: `_loss_metrics()` provides detailed monitoring
- **Flexible Configuration**: Dictionary-based parameter control

### 2. Physics-Informed Constraints
- **Non-negativity Enforcement**: Penalties for negative Δζ predictions
- **Zero-Target Handling**: Special treatment for samples that should predict exactly zero
- **Normalization Awareness**: Penalties applied in physical space when specified

## Configuration Parameters

```python
loss_config = {
    'base_weight': 4.0,           # Weight for zero targets (majority class)
    'non_zero_weight': 3.0,       # Weight for non-zero targets (minority class)
    'lambda_penalty': 0.8,        # Penalty weight for negative predictions
    'zero_penalty_weight': 0.0,   # Additional penalty for zero-target violations
    'use_physical_space': True,   # Whether to apply penalties in physical space
    'penalty_type': 'quadratic'   # 'quadratic' or 'linear' penalty function
}
```

### Parameter Descriptions

#### Weighting Parameters
- **`base_weight`**: Controls importance of zero-target samples (typically majority class)
- **`non_zero_weight`**: Controls importance of non-zero target samples (typically minority class)
- *Recommendation*: Increase `non_zero_weight` if non-zero predictions are more critical

#### Physics Penalty Parameters
- **`lambda_penalty`**: Strength of penalty for negative predictions
  - Conservative: 0.5-1.0
  - Moderate: 1.0-2.0  
  - Aggressive: 2.0-3.0
- **`zero_penalty_weight`**: Additional penalty for predictions that should be exactly zero
  - Start with 0.0-0.3
  - Increase if model struggles with zero predictions

#### Penalty Application
- **`use_physical_space`**: 
  - `True`: Apply penalties after un-normalizing to physical space (recommended)
  - `False`: Apply penalties in normalized space [-1, 1]
- **`penalty_type`**:
  - `'quadratic'`: Stronger penalty for larger violations (recommended)
  - `'linear'`: Gentler, proportional penalty

## Usage Examples

### Basic Usage (Default Configuration)
```python
# Uses default configuration
history = NNz.train_model(
    model_instance=model,
    train_data=train_data,
    val_data=val_data,
    LearningRate=0.001,
    nEpochs=1000,
    bSize=32,
    loss_config=None  # Uses defaults
)
```

### Custom Configuration
```python
# Enhanced physics-informed configuration
enhanced_config = {
    'base_weight': 2.0,
    'non_zero_weight': 5.0,
    'lambda_penalty': 1.5,
    'zero_penalty_weight': 0.3,
    'use_physical_space': True,
    'penalty_type': 'quadratic'
}

history = NNz.train_model(
    model_instance=model,
    train_data=train_data,
    val_data=val_data,
    LearningRate=0.001,
    nEpochs=1000,
    bSize=32,
    loss_config=enhanced_config
)
```

## Monitoring Training

The unified loss function provides comprehensive metrics for monitoring:

- **`total_loss`**: Combined loss value
- **`weighted_mae`**: Base weighted MAE component
- **`negative_penalty`**: Physics penalty for negative predictions
- **`zero_target_penalty`**: Penalty for zero-target violations
- **`negative_predictions_count`**: Number of negative predictions
- **`mean_absolute_error`**: Standard MAE without weighting
- **`max_negative_violation`**: Largest negative prediction violation

### Accessing Training History
```python
# After training, access detailed metrics
print("Final training loss:", history.history['loss'][-1])
print("Weighted MAE:", history.history['weighted_mae_metric'][-1])
print("Negative penalty:", history.history['negative_penalty_metric'][-1])
print("Negative predictions:", history.history['negative_count_metric'][-1])
```

## Recommended Configurations

### 1. Conservative (Good Starting Point)
```python
conservative_config = {
    'base_weight': 3.0,
    'non_zero_weight': 4.0,
    'lambda_penalty': 0.8,
    'zero_penalty_weight': 0.1,
    'use_physical_space': True,
    'penalty_type': 'quadratic'
}
```

### 2. Balanced (Recommended for Most Cases)
```python
balanced_config = {
    'base_weight': 2.0,
    'non_zero_weight': 5.0,
    'lambda_penalty': 1.5,
    'zero_penalty_weight': 0.3,
    'use_physical_space': True,
    'penalty_type': 'quadratic'
}
```

### 3. Aggressive Physics Enforcement
```python
aggressive_config = {
    'base_weight': 1.0,
    'non_zero_weight': 8.0,
    'lambda_penalty': 2.5,
    'zero_penalty_weight': 0.5,
    'use_physical_space': True,
    'penalty_type': 'quadratic'
}
```

## Troubleshooting

### High Negative Penalty
- **Symptom**: `negative_penalty_metric` remains high
- **Solutions**:
  - Increase `lambda_penalty`
  - Check data normalization
  - Consider different activation functions

### Poor Zero Prediction
- **Symptom**: Model predicts non-zero for zero targets
- **Solutions**:
  - Increase `zero_penalty_weight`
  - Adjust `base_weight` vs `non_zero_weight` ratio
  - Consider binary classifier integration

### Training Instability
- **Symptom**: Loss oscillates or doesn't converge
- **Solutions**:
  - Reduce `lambda_penalty`
  - Use `penalty_type='linear'`
  - Lower learning rate
  - Increase `base_weight`

### Physics Constraint Violations (Negative Predictions)
- **Symptom**: Model predicts negative values on test data
- **Root Causes**:
  - Insufficient `lambda_penalty` strength
  - Weak `zero_penalty_weight` for zero targets
  - Scale mismatch between penalties and data magnitude
- **Solutions**:
  - Increase `lambda_penalty` to 5.0-10.0
  - Increase `zero_penalty_weight` to 1.0-2.0
  - Consider `penalty_type='quadratic'` for stronger enforcement
  - Monitor penalty metrics during training

### Specific Configuration for Eliminating Negative Predictions
```python
strict_physics_config = {
    'base_weight': 1.0,           # Lower weight for zero targets
    'non_zero_weight': 8.0,       # Much higher weight for non-zero targets
    'lambda_penalty': 8.0,        # Very strong negative penalty
    'zero_penalty_weight': 1.5,   # Strong zero-target penalty
    'use_physical_space': True,   # Apply in physical space
    'penalty_type': 'quadratic'   # Quadratic penalty for strong enforcement
}
```

## Migration from Old Loss Functions

The old separate loss functions are replaced by the unified approach:

- `_weighted_mae_loss()` → Part of `_unified_loss_function()`
- `_physics_informed_loss()` → Part of `_unified_loss_function()`
- `loss_type='weighted_mae'` → `loss_config` with `lambda_penalty=0`
- `loss_type='physics_informed'` → `loss_config` with `lambda_penalty>0`

## Next Steps

1. **Test with your data**: Start with conservative configuration
2. **Monitor metrics**: Watch all loss components during training
3. **Iterate**: Adjust parameters based on training behavior
4. **Validate**: Check physics constraint violations on test data
5. **Document**: Record successful configurations for future use
