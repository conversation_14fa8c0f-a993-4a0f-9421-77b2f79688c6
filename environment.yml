name: py38
channels:
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - anyio=3.5.0=py38h06a4308_0
  - appdirs=1.4.4=pyhd3eb1b0_0
  - argon2-cffi=21.3.0=pyhd3eb1b0_0
  - argon2-cffi-bindings=21.2.0=py38h7f8727e_0
  - attrs=22.1.0=py38h06a4308_0
  - babel=2.11.0=py38h06a4308_0
  - backcall=0.2.0=pyhd3eb1b0_0
  - blas=1.0=mkl
  - bleach=4.1.0=pyhd3eb1b0_0
  - bottleneck=1.3.5=py38h7deecbd_0
  - brotli=1.0.9=h5eee18b_7
  - brotli-bin=1.0.9=h5eee18b_7
  - brotlipy=0.7.0=py38h27cfd23_1003
  - ca-certificates=2023.05.30=h06a4308_0
  - certifi=2023.7.22=py38h06a4308_0
  - cffi=1.15.1=py38h5eee18b_3
  - charset-normalizer=2.0.4=pyhd3eb1b0_0
  - comm=0.1.2=py38h06a4308_0
  - contourpy=1.0.5=py38hdb19cb5_0
  - cryptography=41.0.2=py38h22a60cf_0
  - cycler=0.11.0=pyhd3eb1b0_0
  - dbus=1.13.18=hb2f20db_0
  - debugpy=1.5.1=py38h295c915_0
  - decorator=5.1.1=pyhd3eb1b0_0
  - defusedxml=0.7.1=pyhd3eb1b0_0
  - entrypoints=0.4=py38h06a4308_0
  - expat=2.4.9=h6a678d5_0
  - fontconfig=2.14.1=h52c9d5c_1
  - fonttools=4.25.0=pyhd3eb1b0_0
  - freetype=2.12.1=h4a9f257_0
  - giflib=5.2.1=h5eee18b_3
  - glib=2.69.1=he621ea3_2
  - gst-plugins-base=1.14.1=h6a678d5_1
  - gstreamer=1.14.1=h5eee18b_1
  - icu=58.2=he6710b0_3
  - idna=3.4=py38h06a4308_0
  - importlib-metadata=6.0.0=py38h06a4308_0
  - importlib_metadata=6.0.0=hd3eb1b0_0
  - importlib_resources=5.2.0=pyhd3eb1b0_1
  - intel-openmp=2023.1.0=hdb19cb5_46305
  - ipykernel=6.19.2=py38hb070fc8_0
  - ipython_genutils=0.2.0=pyhd3eb1b0_1
  - ipywidgets=8.0.4=py38h06a4308_0
  - jpeg=9e=h5eee18b_1
  - json5=0.9.6=pyhd3eb1b0_0
  - jsonschema=4.17.3=py38h06a4308_0
  - jupyter=1.0.0=py38h06a4308_8
  - jupyter_client=7.4.9=py38h06a4308_0
  - jupyter_console=6.6.3=py38h06a4308_0
  - jupyter_core=5.3.0=py38h06a4308_0
  - jupyter_server=1.13.5=pyhd3eb1b0_0
  - jupyterlab=3.3.2=pyhd3eb1b0_0
  - jupyterlab_server=2.10.3=pyhd3eb1b0_1
  - jupyterlab_widgets=3.0.5=py38h06a4308_0
  - jupyterthemes=0.20.0=py_1
  - kiwisolver=1.4.4=py38h6a678d5_0
  - krb5=1.20.1=h143b758_1
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.38=h1181459_1
  - lerc=3.0=h295c915_0
  - lesscpy=0.15.1=pyhd8ed1ab_0
  - libbrotlicommon=1.0.9=h5eee18b_7
  - libbrotlidec=1.0.9=h5eee18b_7
  - libbrotlienc=1.0.9=h5eee18b_7
  - libdeflate=1.17=h5eee18b_0
  - libedit=3.1.20221030=h5eee18b_0
  - libevent=2.1.12=hdbd6064_1
  - libffi=3.4.4=h6a678d5_0
  - libgcc-ng=11.2.0=h1234567_1
  - libgfortran-ng=11.2.0=h00389a5_1
  - libgfortran5=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libllvm10=10.0.1=hbcb73fb_5
  - libllvm14=14.0.6=hdb19cb5_3
  - libpng=1.6.39=h5eee18b_0
  - libpq=12.15=hdbd6064_1
  - libsodium=1.0.18=h7b6447c_0
  - libstdcxx-ng=11.2.0=h1234567_1
  - libtiff=4.5.0=h6a678d5_2
  - libuuid=1.41.5=h5eee18b_0
  - libwebp=1.2.4=h11a3e52_1
  - libwebp-base=1.2.4=h5eee18b_1
  - libxcb=1.15=h7f8727e_0
  - libxkbcommon=1.0.1=hfa300c1_0
  - libxml2=2.9.14=h74e7548_0
  - libxslt=1.1.35=h4e12654_0
  - llvmlite=0.40.0=py38he621ea3_0
  - lz4-c=1.9.4=h6a678d5_0
  - markupsafe=2.1.1=py38h7f8727e_0
  - matplotlib=3.7.1=py38h06a4308_1
  - matplotlib-base=3.7.1=py38h417a72b_1
  - matplotlib-inline=0.1.6=py38h06a4308_0
  - mkl=2023.1.0=h6d00ec8_46342
  - mkl-service=2.4.0=py38h5eee18b_1
  - mkl_fft=1.3.6=py38h417a72b_1
  - mkl_random=1.2.2=py38h417a72b_1
  - munkres=1.1.4=py_0
  - nbclassic=0.5.5=py38h06a4308_0
  - nbformat=5.7.0=py38h06a4308_0
  - ncurses=6.4=h6a678d5_0
  - nest-asyncio=1.5.6=py38h06a4308_0
  - notebook=6.5.4=py38h06a4308_0
  - notebook-shim=0.2.2=py38h06a4308_0
  - nspr=4.35=h6a678d5_0
  - nss=3.89.1=h6a678d5_0
  - numba=0.57.0=py38h1128e8f_0
  - numexpr=2.8.4=py38hc78ab66_1
  - numpy=1.24.3=py38hf6e8229_1
  - numpy-base=1.24.3=py38h060ed82_1
  - openssl=3.0.10=h7f8727e_0
  - packaging=23.0=py38h06a4308_0
  - pandas=1.5.3=py38h417a72b_0
  - pandoc=2.12=h06a4308_3
  - pandocfilters=1.5.0=pyhd3eb1b0_0
  - parso=0.8.3=pyhd3eb1b0_0
  - pcre=8.45=h295c915_0
  - pexpect=4.8.0=pyhd3eb1b0_3
  - pickleshare=0.7.5=pyhd3eb1b0_1003
  - pillow=9.4.0=py38h6a678d5_0
  - pkgutil-resolve-name=1.3.10=py38h06a4308_0
  - platformdirs=2.5.2=py38h06a4308_0
  - ply=3.11=py38_0
  - pooch=1.4.0=pyhd3eb1b0_0
  - prometheus_client=0.14.1=py38h06a4308_0
  - prompt_toolkit=3.0.36=hd3eb1b0_0
  - psutil=5.9.0=py38h5eee18b_0
  - ptyprocess=0.7.0=pyhd3eb1b0_2
  - pure_eval=0.2.2=pyhd3eb1b0_0
  - pycparser=2.21=pyhd3eb1b0_0
  - pygments=2.15.1=py38h06a4308_1
  - pyopenssl=23.2.0=py38h06a4308_0
  - pyparsing=3.0.9=py38h06a4308_0
  - pyqt=5.15.7=py38h6a678d5_1
  - pyqt5-sip=12.11.0=py38h6a678d5_1
  - pyrsistent=0.18.0=py38heee7806_0
  - pysocks=1.7.1=py38h06a4308_0
  - python=3.8.17=h955ad1f_0
  - python-dateutil=2.8.2=pyhd3eb1b0_0
  - python-fastjsonschema=2.16.2=py38h06a4308_0
  - pytz=2022.7=py38h06a4308_0
  - pyzmq=23.2.0=py38h6a678d5_0
  - qt-main=5.15.2=h327a75a_7
  - qt-webengine=5.15.9=hd2b0992_4
  - qtconsole=5.4.2=py38h06a4308_0
  - qtpy=2.2.0=py38h06a4308_0
  - qtwebkit=5.212=h4eab89a_4
  - readline=8.2=h5eee18b_0
  - requests=2.31.0=py38h06a4308_0
  - scipy=1.10.1=py38hf6e8229_1
  - seaborn=0.12.2=py38h06a4308_0
  - send2trash=1.8.0=pyhd3eb1b0_1
  - setuptools=68.0.0=py38h06a4308_0
  - sip=6.6.2=py38h6a678d5_0
  - six=1.16.0=pyhd3eb1b0_1
  - sniffio=1.2.0=py38h06a4308_1
  - sqlite=3.41.2=h5eee18b_0
  - stack_data=0.2.0=pyhd3eb1b0_0
  - tbb=2021.8.0=hdb19cb5_0
  - terminado=0.17.1=py38h06a4308_0
  - testpath=0.6.0=py38h06a4308_0
  - tk=8.6.12=h1ccaba5_0
  - toml=0.10.2=pyhd3eb1b0_0
  - tornado=6.2=py38h5eee18b_0
  - urllib3=1.26.16=py38h06a4308_0
  - webencodings=0.5.1=py38_1
  - websocket-client=0.58.0=py38h06a4308_4
  - wheel=0.38.4=py38h06a4308_0
  - widgetsnbextension=4.0.5=py38h06a4308_0
  - xz=5.4.2=h5eee18b_0
  - zeromq=4.3.4=h2531618_0
  - zipp=3.11.0=py38h06a4308_0
  - zlib=1.2.13=h5eee18b_0
  - zstd=1.5.5=hc292b87_0
  - pip:
      - absl-py==2.1.0
      - asttokens==2.2.1
      - astunparse==1.6.3
      - beautifulsoup4==4.12.3
      - cachetools==5.3.2
      - executing==1.2.0
      - flatbuffers==23.5.26
      - gast==0.4.0
      - google-auth==2.27.0
      - google-auth-oauthlib==1.0.0
      - google-pasta==0.2.0
      - grpcio==1.60.1
      - h5py==3.10.0
      - ipython==8.12.2
      - jedi==0.18.2
      - jinja2==3.1.4
      - jupyter-server==1.24.0
      - jupyterlab-pygments==0.3.0
      - keras==2.13.1
      - libclang==16.0.6
      - lxml==5.2.2
      - markdown==3.5.2
      - mistune==3.0.2
      - nbclient==0.10.0
      - nbconvert==7.16.4
      - numpy-stl==3.0.1
      - oauthlib==3.2.2
      - opt-einsum==3.3.0
      - pip==24.0
      - prompt-toolkit==3.0.38
      - protobuf==4.25.2
      - pyasn1==0.5.1
      - pyasn1-modules==0.3.0
      - python-docx==1.1.2
      - python-utils==3.7.0
      - requests-oauthlib==1.3.1
      - rsa==4.9
      - soupsieve==2.5
      - stack-data==0.6.2
      - tensorboard==2.13.0
      - tensorboard-data-server==0.7.2
      - tensorflow==2.13.1
      - tensorflow-estimator==2.13.0
      - tensorflow-io-gcs-filesystem==0.34.0
      - termcolor==2.4.0
      - tinycss2==1.3.0
      - traitlets==5.9.0
      - typing-extensions==4.11.0
      - wcwidth==0.2.6
      - werkzeug==3.0.1
      - wrapt==1.16.0
prefix: /home/<USER>/anaconda3/envs/py38
